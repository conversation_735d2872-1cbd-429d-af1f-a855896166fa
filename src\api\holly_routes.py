"""
Holly AI Routes - LLM-first API where <PERSON> AI Brain orchestrates everything
This is the main interface - users only interact with <PERSON>, not technical endpoints
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.core.holly_ai_brain import HollyAIBrain
from src.services.execution_service import ExecutionService

# Create router
holly_router = APIRouter(prefix="/holly", tags=["Holly AI"])

# Global Holly AI instance
holly_brain = HollyAIBrain()
execution_service = ExecutionService()


# Request/Response models
class HollyRequest(BaseModel):
    message: str
    user_context: Optional[Dict[str, Any]] = None


class HollyResponse(BaseModel):
    response: str
    type: str
    requires_action: bool = False
    trading_plan: Optional[Dict[str, Any]] = None
    plan_id: Optional[str] = None
    function_called: Optional[str] = None
    timestamp: str


class ExecutePlanRequest(BaseModel):
    plan_id: str
    confirm: bool = True


# Main Holly AI endpoint - this is where all the magic happens
@holly_router.post("/chat", response_model=HollyResponse)
async def chat_with_holly(request: Holly<PERSON><PERSON>quest):
    """
    Main endpoint - chat with Holly AI about anything trading related.
    Holly will understand your request and take appropriate action.
    
    Examples:
    - "Make me $50 today"
    - "Find me a good momentum play"
    - "What's AAPL looking like right now?"
    - "I need a hedge for my Tesla position"
    - "Show me some TTM Squeeze setups"
    """
    try:
        # Process the message through Holly's brain
        result = await holly_brain.process_user_message(
            user_message=request.message,
            user_context=request.user_context
        )
        
        return HollyResponse(
            response=result.get("response", ""),
            type=result.get("type", "chat"),
            requires_action=result.get("requires_action", False),
            trading_plan=result.get("trading_plan"),
            plan_id=result.get("plan_id"),
            function_called=result.get("function_called"),
            timestamp=datetime.utcnow().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Holly encountered an error: {str(e)}")


@holly_router.post("/execute")
async def execute_trading_plan(request: ExecutePlanRequest, background_tasks: BackgroundTasks):
    """
    Execute a trading plan created by Holly AI.
    This will submit all orders to the paper trading account.
    """
    try:
        # Get the trading plan from Holly's memory
        plan_found = False
        trading_plan = None
        
        # Search Holly's conversation history for the plan
        for msg in holly_brain.conversation_history:
            if (msg.get("function_called") == "create_trading_plan" and 
                msg.get("function_result", {}).get("plan_id") == request.plan_id):
                trading_plan = msg["function_result"]["plan"]
                plan_found = True
                break
                
        if not plan_found or not trading_plan:
            raise HTTPException(status_code=404, detail="Trading plan not found")
            
        # Execute the orders
        executed_orders = []
        failed_orders = []
        
        for order_data in trading_plan.get("orders", []):
            try:
                from src.models.trading import Order
                
                # Recreate order object
                order = Order(**order_data)
                
                # Submit to execution service
                order_id = await execution_service.submit_order(order)
                
                if order_id:
                    executed_orders.append({
                        "symbol": order.symbol,
                        "side": order.side.value,
                        "quantity": float(order.qty),
                        "order_id": order_id
                    })
                else:
                    failed_orders.append({
                        "symbol": order.symbol,
                        "error": "Failed to submit order"
                    })
                    
            except Exception as e:
                failed_orders.append({
                    "symbol": order_data.get("symbol", "Unknown"),
                    "error": str(e)
                })
                
        # Update Holly's conversation with execution results
        execution_message = f"Executed {len(executed_orders)} orders successfully."
        if failed_orders:
            execution_message += f" {len(failed_orders)} orders failed."
            
        holly_brain.conversation_history.append({
            "role": "system",
            "content": f"Trading plan {request.plan_id} execution: {execution_message}",
            "timestamp": datetime.utcnow().isoformat(),
            "execution_result": {
                "executed_orders": executed_orders,
                "failed_orders": failed_orders
            }
        })
        
        return {
            "success": True,
            "plan_id": request.plan_id,
            "executed_orders": executed_orders,
            "failed_orders": failed_orders,
            "message": execution_message
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@holly_router.get("/conversation")
async def get_conversation_history(limit: int = 20):
    """Get recent conversation history with Holly"""
    try:
        # Return recent conversation
        recent_history = holly_brain.conversation_history[-limit:]
        
        # Format for display
        formatted_history = []
        for msg in recent_history:
            formatted_msg = {
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg["timestamp"]
            }
            
            # Add function call info if present
            if msg.get("function_called"):
                formatted_msg["function_called"] = msg["function_called"]
                formatted_msg["had_function_result"] = bool(msg.get("function_result"))
                
            formatted_history.append(formatted_msg)
            
        return {
            "conversation": formatted_history,
            "total_messages": len(holly_brain.conversation_history)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@holly_router.post("/reset")
async def reset_conversation():
    """Reset Holly's conversation history (start fresh)"""
    try:
        holly_brain.conversation_history = []
        holly_brain.user_context = {}
        
        return {
            "success": True,
            "message": "Conversation reset. Holly is ready for a fresh start!"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@holly_router.get("/capabilities")
async def get_holly_capabilities():
    """Get information about Holly's capabilities"""
    return {
        "name": "Holly AI",
        "description": "AI-powered trading assistant with natural language interface",
        "capabilities": [
            "Natural language trading request interpretation",
            "Real-time market data analysis",
            "Technical indicator analysis (TTM Squeeze, MACD, etc.)",
            "AI-enhanced stop loss calculation using support/resistance",
            "Trading opportunity discovery and screening",
            "News sentiment analysis",
            "Fundamental analysis",
            "Position sizing and risk management",
            "Hedging strategy creation",
            "Complete trading plan generation",
            "Paper trading execution"
        ],
        "example_requests": [
            "Make me $50 today",
            "Find me a good momentum play under $100",
            "What's AAPL looking like right now?",
            "I need a hedge for my 100 shares of TSLA",
            "Show me some TTM Squeeze setups in tech stocks",
            "What's the sentiment on NVDA?",
            "Calculate position size for MSFT at $300 with $5 stop",
            "Find me some oversold stocks ready to bounce"
        ],
        "functions": list(holly_brain.functions.keys()),
        "trading_mode": "Paper Trading Only",
        "risk_management": "All trades include proper stop losses and position sizing"
    }


@holly_router.get("/status")
async def get_holly_status():
    """Get Holly's current status and context"""
    try:
        return {
            "status": "active",
            "conversation_length": len(holly_brain.conversation_history),
            "user_context": holly_brain.user_context,
            "last_activity": holly_brain.conversation_history[-1]["timestamp"] if holly_brain.conversation_history else None,
            "available_functions": len(holly_brain.functions),
            "model": holly_brain.model,
            "temperature": holly_brain.temperature
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Quick action endpoints for common requests
@holly_router.get("/quick/opportunities")
async def quick_find_opportunities(strategy: str = "any", confidence: float = 0.7):
    """Quick endpoint to find trading opportunities"""
    try:
        request_message = f"Find me {strategy} trading opportunities with at least {confidence:.0%} confidence"
        
        result = await holly_brain.process_user_message(request_message)
        
        return {
            "opportunities": result.get("function_result", {}).get("opportunities", []),
            "holly_analysis": result.get("response", "")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@holly_router.get("/quick/quote/{symbol}")
async def quick_get_quote(symbol: str):
    """Quick endpoint to get a quote with Holly's analysis"""
    try:
        request_message = f"What's {symbol.upper()} looking like right now?"
        
        result = await holly_brain.process_user_message(request_message)
        
        return {
            "symbol": symbol.upper(),
            "quote_data": result.get("function_result", {}),
            "holly_analysis": result.get("response", "")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@holly_router.post("/quick/plan")
async def quick_create_plan(profit_target: float, risk_tolerance: str = "moderate"):
    """Quick endpoint to create a trading plan"""
    try:
        request_message = f"Make me ${profit_target} with {risk_tolerance} risk"
        
        result = await holly_brain.process_user_message(request_message)
        
        return {
            "trading_plan": result.get("trading_plan"),
            "plan_id": result.get("plan_id"),
            "holly_explanation": result.get("response", ""),
            "requires_execution": result.get("requires_action", False)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
