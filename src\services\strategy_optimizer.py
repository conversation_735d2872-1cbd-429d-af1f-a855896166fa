"""
Strategy optimization service for nightly re-optimization of trading strategies
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import numpy as np
import pandas as pd
from concurrent.futures import ProcessPoolExecutor

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import TimeFrame
from src.services.storage_service import StorageService
from src.services.data_ingestion import DataIngestionService


@dataclass
class StrategyVariant:
    """A variant of a trading strategy with specific parameters"""
    name: str
    parameters: Dict[str, Any]
    fitness_score: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    total_trades: int = 0


class StrategyOptimizer:
    """Service for optimizing trading strategies through backtesting"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.storage = StorageService()
        self.data_service = DataIngestionService()
        
        # Strategy variants to test
        self.strategy_variants: List[StrategyVariant] = []
        self.top_strategies: List[StrategyVariant] = []
        
        # Optimization settings
        self.lookback_days = settings.BACKTEST_LOOKBACK_DAYS
        self.max_workers = 4
        
    async def start_nightly_optimization(self):
        """Start the nightly optimization process"""
        self.logger.info("Starting nightly strategy optimization...")
        
        try:
            # Generate strategy variants
            await self.generate_strategy_variants()
            
            # Run backtests in parallel
            await self.run_parallel_backtests()
            
            # Select top performers
            await self.select_top_strategies()
            
            # Update live strategies
            await self.update_live_strategies()
            
            self.logger.info(f"Optimization complete. Selected {len(self.top_strategies)} top strategies")
            
        except Exception as e:
            self.logger.error(f"Error in nightly optimization: {e}")
            
    async def generate_strategy_variants(self):
        """Generate variants of TTM Squeeze strategy with different parameters"""
        self.strategy_variants = []
        
        # TTM Squeeze parameter ranges
        bb_periods = [18, 20, 22]
        bb_stddevs = [1.8, 2.0, 2.2]
        kc_periods = [18, 20, 22]
        kc_multipliers = [1.3, 1.5, 1.7]
        ema_fast = [8, 10, 12]
        ema_slow = [20, 21, 26]
        
        variant_id = 0
        for bb_period in bb_periods:
            for bb_stddev in bb_stddevs:
                for kc_period in kc_periods:
                    for kc_mult in kc_multipliers:
                        for ema_f in ema_fast:
                            for ema_s in ema_slow:
                                if ema_f < ema_s:  # Fast must be less than slow
                                    variant = StrategyVariant(
                                        name=f"TTM_Squeeze_v{variant_id}",
                                        parameters={
                                            'bb_period': bb_period,
                                            'bb_stddev': bb_stddev,
                                            'kc_period': kc_period,
                                            'kc_multiplier': kc_mult,
                                            'ema_fast': ema_f,
                                            'ema_slow': ema_s,
                                            'volume_filter': True,
                                            'min_volume_ratio': 0.8
                                        }
                                    )
                                    self.strategy_variants.append(variant)
                                    variant_id += 1
                                    
        self.logger.info(f"Generated {len(self.strategy_variants)} strategy variants")
        
    async def run_parallel_backtests(self):
        """Run backtests for all strategy variants in parallel"""
        # Get test symbols (top liquid stocks)
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']
        
        # Prepare backtest tasks
        tasks = []
        for variant in self.strategy_variants:
            for symbol in test_symbols:
                tasks.append(self.backtest_strategy_variant(variant, symbol))
                
        # Run backtests with limited concurrency
        semaphore = asyncio.Semaphore(self.max_workers)
        
        async def run_with_semaphore(task):
            async with semaphore:
                return await task
                
        results = await asyncio.gather(*[run_with_semaphore(task) for task in tasks])
        
        # Aggregate results by variant
        variant_results = {}
        for result in results:
            if result:
                variant_name = result['variant_name']
                if variant_name not in variant_results:
                    variant_results[variant_name] = []
                variant_results[variant_name].append(result)
                
        # Calculate fitness scores
        for variant in self.strategy_variants:
            if variant.name in variant_results:
                variant_data = variant_results[variant.name]
                variant.fitness_score = self.calculate_fitness_score(variant_data)
                
    async def backtest_strategy_variant(self, variant: StrategyVariant, symbol: str) -> Optional[Dict]:
        """Backtest a single strategy variant on a symbol"""
        try:
            # Get historical data
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=self.lookback_days)
            
            data = await self.data_service.get_historical_data(
                symbol=symbol,
                timeframe=TimeFrame.MINUTE_15,
                start=start_date,
                end=end_date,
                limit=10000
            )
            
            if len(data) < 100:
                return None
                
            # Run backtest simulation
            trades = self.simulate_strategy(data, variant.parameters)
            
            if not trades:
                return None
                
            # Calculate performance metrics
            returns = [trade['pnl'] for trade in trades]
            
            if not returns:
                return None
                
            total_return = sum(returns)
            win_rate = len([r for r in returns if r > 0]) / len(returns)
            
            # Calculate Sharpe ratio (simplified)
            if len(returns) > 1:
                sharpe = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            else:
                sharpe = 0
                
            # Calculate max drawdown
            cumulative = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative)
            drawdown = cumulative - running_max
            max_drawdown = abs(np.min(drawdown)) if len(drawdown) > 0 else 0
            
            return {
                'variant_name': variant.name,
                'symbol': symbol,
                'total_return': total_return,
                'win_rate': win_rate,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_drawdown,
                'num_trades': len(trades)
            }
            
        except Exception as e:
            self.logger.error(f"Error backtesting {variant.name} on {symbol}: {e}")
            return None
            
    def simulate_strategy(self, data, parameters) -> List[Dict]:
        """Simulate strategy execution on historical data"""
        # This is a simplified simulation - would implement full strategy logic
        trades = []
        
        # Convert to DataFrame for easier manipulation
        df_data = []
        for item in data:
            df_data.append({
                'timestamp': item.timestamp,
                'open': float(item.open),
                'high': float(item.high),
                'low': float(item.low),
                'close': float(item.close),
                'volume': item.volume
            })
            
        df = pd.DataFrame(df_data)
        
        if len(df) < 50:
            return trades
            
        # Calculate indicators with variant parameters
        from src.indicators.technical_indicators import TechnicalIndicatorEngine
        indicator_engine = TechnicalIndicatorEngine()
        
        # Simulate TTM Squeeze signals
        for i in range(50, len(df)):
            window_data = df.iloc[i-50:i+1]
            
            # Calculate BB and KC with variant parameters
            bb_upper, bb_lower = indicator_engine.bollinger_bands(
                window_data['close'], 
                period=parameters['bb_period'],
                std_dev=parameters['bb_stddev']
            )
            
            kc_upper, kc_lower = indicator_engine.keltner_channels(
                window_data['high'],
                window_data['low'], 
                window_data['close'],
                period=parameters['kc_period'],
                multiplier=parameters['kc_multiplier']
            )
            
            # Check squeeze condition
            if (bb_upper.iloc[-1] <= kc_upper.iloc[-1] and 
                bb_lower.iloc[-1] >= kc_lower.iloc[-1]):
                
                # Simulate trade entry
                entry_price = df.iloc[i]['close']
                
                # Simple exit after 10 bars or 2% move
                exit_idx = min(i + 10, len(df) - 1)
                exit_price = df.iloc[exit_idx]['close']
                
                pnl = exit_price - entry_price  # Simplified P&L
                
                trades.append({
                    'entry_time': df.iloc[i]['timestamp'],
                    'entry_price': entry_price,
                    'exit_time': df.iloc[exit_idx]['timestamp'],
                    'exit_price': exit_price,
                    'pnl': pnl
                })
                
        return trades
        
    def calculate_fitness_score(self, variant_results: List[Dict]) -> float:
        """Calculate fitness score for a strategy variant"""
        if not variant_results:
            return 0.0
            
        # Aggregate metrics across all symbols
        total_return = sum(r['total_return'] for r in variant_results)
        avg_win_rate = np.mean([r['win_rate'] for r in variant_results])
        avg_sharpe = np.mean([r['sharpe_ratio'] for r in variant_results])
        max_drawdown = max([r['max_drawdown'] for r in variant_results])
        total_trades = sum(r['num_trades'] for r in variant_results)
        
        # Fitness function: Sharpe / MaxDD * WinRate
        if max_drawdown > 0 and total_trades > 10:
            fitness = (avg_sharpe / max_drawdown) * avg_win_rate
        else:
            fitness = 0.0
            
        return fitness
        
    async def select_top_strategies(self):
        """Select top performing strategies"""
        # Sort by fitness score
        self.strategy_variants.sort(key=lambda x: x.fitness_score, reverse=True)
        
        # Select top 10 strategies
        self.top_strategies = self.strategy_variants[:10]
        
        self.logger.info("Top 5 strategies:")
        for i, strategy in enumerate(self.top_strategies[:5]):
            self.logger.info(f"{i+1}. {strategy.name}: fitness={strategy.fitness_score:.4f}")
            
    async def update_live_strategies(self):
        """Update live trading strategies with optimized parameters"""
        # Store top strategies for use by signal engine
        for strategy in self.top_strategies:
            await self.storage.store_cache(
                f"optimized_strategy:{strategy.name}",
                strategy.__dict__,
                ttl=86400  # 24 hours
            )
            
        # Store list of top strategy names
        top_names = [s.name for s in self.top_strategies]
        await self.storage.store_cache(
            "top_strategies",
            top_names,
            ttl=86400
        )
