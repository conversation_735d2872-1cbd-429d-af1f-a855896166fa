# Holly AI Trading System

An AI-powered trading system that combines quantitative analysis with ChatGPT's natural language processing to create comprehensive trading plans. Users can simply ask "Make me $50 today" and Holly AI will analyze the market, generate signals, and create a complete trading strategy with risk management.

## 🚀 Features

### Core Capabilities
- **Natural Language Trading Requests**: Ask Holly AI in plain English to create trading plans
- **TTM Squeeze Signal Detection**: Automated detection of TTM Squeeze setups across multiple timeframes
- **AI-Enhanced Stop Losses**: ChatGPT analyzes pivot points, support/resistance for optimal stops
- **Nightly Strategy Optimization**: Automated backtesting and parameter optimization
- **Dynamic Universe Management**: Intelligent stock screening and universe updates
- **Real-time Market Data**: Live data streaming from Alpaca and Financial Modeling Prep (FMP)
- **Comprehensive Risk Management**: Position sizing, portfolio limits, and circuit breakers
- **Paper Trading Mode**: Safe testing environment for learning and strategy validation

### Technical Features
- **Multi-timeframe Analysis**: 1m, 5m, 15m, 30m, 1h, and daily timeframes
- **Advanced Technical Indicators**: TTM Squeeze, MACD, EMAs, Bollinger Bands, Keltner Channels, ATR
- **Support/Resistance Detection**: Automated pivot point and S&R level identification
- **Strategy Optimization**: Nightly backtesting with parallel parameter optimization
- **Universe Management**: Dynamic stock screening and symbol universe updates
- **LLM Integration**: ChatGPT function calling with custom FMP endpoint access
- **Scheduled Tasks**: Automated nightly optimization and universe updates
- **Real-time Monitoring**: Prometheus metrics and Grafana dashboards
- **Microservices Architecture**: Scalable, containerized services

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   LLM Service   │    │  Data Ingestion │
│    (React)      │    │   (ChatGPT)     │    │   (Alpaca/FMP)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │            Trading Orchestrator                 │
         └─────────────────────────────────────────────────┘
                                 │
    ┌────────────┬────────────────┼────────────────┬────────────┐
    │            │                │                │            │
┌───▼───┐  ┌────▼────┐  ┌────────▼────────┐  ┌───▼───┐  ┌────▼────┐
│Signal │  │Feature  │  │   Execution     │  │ Risk  │  │Storage  │
│Engine │  │Engine   │  │   Service       │  │ Mgmt  │  │Service  │
└───────┘  └─────────┘  └─────────────────┘  └───────┘  └─────────┘
```

## 🛠️ Installation

### Prerequisites
- Docker and Docker Compose
- Python 3.11+
- Node.js 18+
- API Keys for:
  - Alpaca (Paper Trading)
  - Financial Modeling Prep
  - OpenAI ChatGPT

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CHatbotfinal
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Web Interface: http://localhost:3001
   - API: http://localhost:8080
   - Grafana: http://localhost:3000 (admin/admin)

### Manual Installation

1. **Backend Setup**
   ```bash
   pip install -r requirements.txt
   python main.py
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

## 📊 Usage

### Basic Trading Requests

Holly AI understands natural language requests:

```
"Make me $50 today"
"Find me a good trade with low risk"
"I want to make $100 with hedging"
"Show me TTM Squeeze setups"
```

### API Endpoints

- `POST /api/v1/chat` - Chat with Holly AI
- `GET /api/v1/signals` - Get live trading signals
- `POST /api/v1/execute-plan` - Execute a trading plan
- `GET /api/v1/positions` - Get current positions
- `GET /api/v1/account` - Get account information

### Example Chat Interaction

```json
{
  "message": "Make me $50 today"
}
```

Response:
```json
{
  "response": "I've found a TTM Squeeze setup on AAPL...",
  "trading_plan": {
    "goal": {"profit_target": 50},
    "signals": [...],
    "orders": [...],
    "estimated_profit": 52.50,
    "estimated_risk": 15.00
  },
  "plan_id": "uuid-here"
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Alpaca API (Paper Trading)
APCA_API_BASE_URL=https://paper-api.alpaca.markets
APCA_API_KEY_ID=your_paper_key
APCA_API_SECRET_KEY=your_paper_secret

# Financial Modeling Prep
FMP_API_KEY=your_fmp_key

# OpenAI
OPENAI_API_KEY=your_openai_key

# Trading Settings
PAPER_TRADING=True
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10
```

### TTM Squeeze Parameters

```bash
TTM_BB_PERIOD=20
TTM_BB_STDDEV=2.0
TTM_KC_PERIOD=20
TTM_KC_MULTIPLIER=1.5
```

## 📈 Trading Strategies

### TTM Squeeze Strategy
- Detects when Bollinger Bands are inside Keltner Channels
- Waits for "squeeze firing" (bands expand outside channels)
- Uses momentum direction for trade direction
- Applies trend and volume filters

### Risk Management
- AI-enhanced stop losses using technical analysis
- Position sizing based on volatility (ATR)
- Portfolio-level risk limits
- Circuit breakers for excessive losses

## 🔍 Monitoring

### Prometheus Metrics
- Request counts and latencies
- Signal generation rates
- Order execution metrics
- Account balance and P&L
- Error rates by service

### Grafana Dashboards
- Real-time trading performance
- Signal analysis
- System health monitoring
- Account metrics

## 🧪 Testing

```bash
# Run unit tests
pytest tests/

# Run integration tests
pytest tests/integration/

# Run with coverage
pytest --cov=src tests/
```

## 🚨 Safety Features

- **Paper Trading Only**: All trades execute in simulation mode
- **Circuit Breakers**: Automatic trading halt on excessive losses
- **Position Limits**: Maximum number of concurrent positions
- **Risk Controls**: Per-trade and portfolio-level risk management
- **Order Validation**: Comprehensive pre-trade checks

## 📝 Development

### Adding New Strategies
1. Create strategy in `src/services/signal_engine.py`
2. Add configuration in `StrategyConfig`
3. Implement signal detection logic
4. Add tests

### Adding New Indicators
1. Add indicator to `src/indicators/technical_indicators.py`
2. Update `TechnicalIndicators` model
3. Integrate with feature engine

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## ⚠️ Disclaimer

This software is for educational and research purposes only. It operates in paper trading mode and does not execute real trades. Past performance does not guarantee future results. Always do your own research and consider consulting with a financial advisor before making investment decisions.

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- Documentation: See `/docs` folder
- Issues: GitHub Issues
- API Reference: http://localhost:8080/docs (when running)

---

**Holly AI Trading System** - Where AI meets quantitative trading 🤖📈
