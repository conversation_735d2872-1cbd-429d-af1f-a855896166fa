# Holly AI Trading System

**The world's first LLM-first trading system** where <PERSON><PERSON><PERSON><PERSON> is the primary orchestrator for all trading decisions. Holly AI understands natural language requests like "Make me $50 today" and autonomously creates comprehensive trading plans with proper risk management, technical analysis, and execution - all through conversational AI.

Unlike traditional trading systems with AI assistance, <PERSON> AI **IS** the trading system. Every decision flows through the LLM brain, making professional-grade trading accessible to anyone who can chat.

## 🚀 Features

### 🧠 LLM-First Architecture
- **Holly AI Brain**: ChatGPT orchestrates ALL trading decisions through function calling
- **Natural Language Interface**: No technical jargon - just chat like you're talking to a professional trader
- **Autonomous Decision Making**: <PERSON> interprets goals, analyzes markets, and creates complete trading plans
- **Intelligent Function Calling**: 15+ specialized functions for market analysis, risk management, and execution
- **Conversational Memory**: <PERSON> remembers your preferences and trading history across sessions

### 🚀 Core Trading Capabilities
- **TTM Squeeze Detection**: Real-time scanning across multiple timeframes with AI-enhanced filtering
- **AI-Enhanced Stop Losses**: ChatGPT analyzes pivot points, support/resistance, and volatility for optimal stops
- **Dynamic Universe Management**: Intelligent stock screening and daily universe updates (100+ symbols)
- **Nightly Strategy Optimization**: Automated backtesting with parallel parameter optimization
- **News Sentiment Analysis**: Real-time sentiment analysis integrated into trading decisions
- **Hedging Strategies**: Automatic hedge creation for risk management
- **Position Sizing**: AI-calculated position sizes based on account risk and technical levels

### Technical Features
- **Multi-timeframe Analysis**: 1m, 5m, 15m, 30m, 1h, and daily timeframes
- **Advanced Technical Indicators**: TTM Squeeze, MACD, EMAs, Bollinger Bands, Keltner Channels, ATR
- **Support/Resistance Detection**: Automated pivot point and S&R level identification
- **Strategy Optimization**: Nightly backtesting with parallel parameter optimization
- **Universe Management**: Dynamic stock screening and symbol universe updates
- **LLM Integration**: ChatGPT function calling with custom FMP endpoint access
- **Scheduled Tasks**: Automated nightly optimization and universe updates
- **Real-time Monitoring**: Prometheus metrics and Grafana dashboards
- **Microservices Architecture**: Scalable, containerized services

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   LLM Service   │    │  Data Ingestion │
│    (React)      │    │   (ChatGPT)     │    │   (Alpaca/FMP)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │            Trading Orchestrator                 │
         └─────────────────────────────────────────────────┘
                                 │
    ┌────────────┬────────────────┼────────────────┬────────────┐
    │            │                │                │            │
┌───▼───┐  ┌────▼────┐  ┌────────▼────────┐  ┌───▼───┐  ┌────▼────┐
│Signal │  │Feature  │  │   Execution     │  │ Risk  │  │Storage  │
│Engine │  │Engine   │  │   Service       │  │ Mgmt  │  │Service  │
└───────┘  └─────────┘  └─────────────────┘  └───────┘  └─────────┘
```

## 🛠️ Installation

### Prerequisites
- Docker and Docker Compose
- Python 3.11+
- Node.js 18+
- API Keys for:
  - Alpaca (Paper Trading)
  - Financial Modeling Prep
  - OpenAI ChatGPT

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CHatbotfinal
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Web Interface: http://localhost:3001
   - API: http://localhost:8080
   - Grafana: http://localhost:3000 (admin/admin)

### Manual Installation

1. **Backend Setup**
   ```bash
   pip install -r requirements.txt
   python main.py
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

## 💬 Usage - Just Chat with Holly!

### Natural Language Trading Requests

Holly AI understands ANY trading request in plain English:

**Profit-focused requests:**
- "Make me $50 today"
- "I want to make $200 this week with moderate risk"
- "Find me a safe trade that could make $25"

**Analysis requests:**
- "What's AAPL looking like right now?"
- "Show me some TTM Squeeze setups in tech stocks"
- "Find me momentum plays under $100"
- "What's the sentiment on NVDA?"

**Risk management requests:**
- "I need a hedge for my 100 shares of Tesla"
- "Calculate position size for MSFT at $300 with a $5 stop"
- "Find me some defensive plays for my portfolio"

**Learning requests:**
- "Explain why this trade makes sense"
- "What should I watch out for with this setup?"
- "How do you calculate stop losses?"

### 🔌 API Endpoints

**Primary Holly AI Interface:**
- `POST /api/v1/holly/chat` - Main chat endpoint (handles everything)
- `POST /api/v1/holly/execute` - Execute Holly's trading plans
- `GET /api/v1/holly/conversation` - View chat history
- `GET /api/v1/holly/capabilities` - See what Holly can do

**Quick Action Endpoints:**
- `GET /api/v1/holly/quick/opportunities` - Fast opportunity scan
- `GET /api/v1/holly/quick/quote/{symbol}` - Quick quote with analysis
- `POST /api/v1/holly/quick/plan` - Fast plan generation

**Legacy Technical Endpoints** (for advanced users):
- `GET /api/v1/signals` - Raw trading signals
- `GET /api/v1/positions` - Current positions
- `GET /api/v1/account` - Account information

### 💡 Example Conversation

**User:** "Make me $50 today"

**Holly AI:** "I'll help you make $50 today! Let me analyze the market and find some good opportunities...

*[Holly calls find_trading_opportunities and get_market_data_with_indicators]*

I found a great TTM Squeeze setup on AAPL that just fired with bullish momentum on the 15-minute chart. Here's my plan:

**Trade Setup:**
- **BUY 25 shares of AAPL** at $150.50
- **Stop Loss:** $148.75 (AI-calculated based on recent support level)
- **Target:** $152.25 (2:1 risk/reward ratio)
- **Risk:** $43.75 | **Potential Profit:** $87.50

**Why this trade:**
1. TTM Squeeze just fired with strong bullish momentum
2. Price broke above the 21 EMA with volume confirmation
3. AI analysis shows strong support at $148.75 (recent swing low)
4. News sentiment on AAPL is neutral to positive

This gives us a 67% chance of success based on historical TTM Squeeze performance. Would you like me to execute this plan?"

**User:** "Yes, execute it"

**Holly AI:** "✅ Plan executed successfully!

**Orders placed:**
- BUY 25 AAPL @ $150.50 (Bracket order with stop at $148.75, target at $152.25)

I'll monitor this trade and let you know when we hit our target or if anything changes. You can ask me 'How's my AAPL trade doing?' anytime for updates."

## 🔧 Configuration

### Environment Variables

```bash
# Alpaca API (Paper Trading)
APCA_API_BASE_URL=https://paper-api.alpaca.markets
APCA_API_KEY_ID=your_paper_key
APCA_API_SECRET_KEY=your_paper_secret

# Financial Modeling Prep
FMP_API_KEY=your_fmp_key

# OpenAI
OPENAI_API_KEY=your_openai_key

# Trading Settings
PAPER_TRADING=True
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10
```

### TTM Squeeze Parameters

```bash
TTM_BB_PERIOD=20
TTM_BB_STDDEV=2.0
TTM_KC_PERIOD=20
TTM_KC_MULTIPLIER=1.5
```

## 📈 Trading Strategies

### TTM Squeeze Strategy
- Detects when Bollinger Bands are inside Keltner Channels
- Waits for "squeeze firing" (bands expand outside channels)
- Uses momentum direction for trade direction
- Applies trend and volume filters

### Risk Management
- AI-enhanced stop losses using technical analysis
- Position sizing based on volatility (ATR)
- Portfolio-level risk limits
- Circuit breakers for excessive losses

## 🔍 Monitoring

### Prometheus Metrics
- Request counts and latencies
- Signal generation rates
- Order execution metrics
- Account balance and P&L
- Error rates by service

### Grafana Dashboards
- Real-time trading performance
- Signal analysis
- System health monitoring
- Account metrics

## 🧪 Testing

```bash
# Run unit tests
pytest tests/

# Run integration tests
pytest tests/integration/

# Run with coverage
pytest --cov=src tests/
```

## 🚨 Safety Features

- **Paper Trading Only**: All trades execute in simulation mode
- **Circuit Breakers**: Automatic trading halt on excessive losses
- **Position Limits**: Maximum number of concurrent positions
- **Risk Controls**: Per-trade and portfolio-level risk management
- **Order Validation**: Comprehensive pre-trade checks

## 📝 Development

### Adding New Strategies
1. Create strategy in `src/services/signal_engine.py`
2. Add configuration in `StrategyConfig`
3. Implement signal detection logic
4. Add tests

### Adding New Indicators
1. Add indicator to `src/indicators/technical_indicators.py`
2. Update `TechnicalIndicators` model
3. Integrate with feature engine

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## ⚠️ Disclaimer

This software is for educational and research purposes only. It operates in paper trading mode and does not execute real trades. Past performance does not guarantee future results. Always do your own research and consider consulting with a financial advisor before making investment decisions.

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- Documentation: See `/docs` folder
- Issues: GitHub Issues
- API Reference: http://localhost:8080/docs (when running)

---

**Holly AI Trading System** - Where AI meets quantitative trading 🤖📈
