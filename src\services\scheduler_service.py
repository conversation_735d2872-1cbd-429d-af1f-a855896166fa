"""
Scheduler service for running periodic tasks like nightly optimization
"""

import asyncio
import logging
from datetime import datetime, time
from typing import List, Callable, Dict, Any

from src.core.config import settings
from src.core.logging import get_logger
from src.services.strategy_optimizer import StrategyOptimizer
from src.services.universe_manager import UniverseManager


class SchedulerService:
    """Service for scheduling and running periodic tasks"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
        # Initialize services for scheduled tasks
        self.strategy_optimizer = StrategyOptimizer()
        self.universe_manager = UniverseManager()
        
        # Schedule configuration
        self.schedules = {
            'nightly_optimization': {
                'time': time(2, 0),  # 2:00 AM
                'function': self.run_nightly_optimization,
                'enabled': True
            },
            'universe_update': {
                'time': time(1, 0),  # 1:00 AM
                'function': self.run_universe_update,
                'enabled': True
            },
            'strategy_performance_check': {
                'time': time(3, 0),  # 3:00 AM
                'function': self.run_performance_check,
                'enabled': True
            }
        }
        
    async def start(self):
        """Start the scheduler service"""
        self.logger.info("Starting scheduler service...")
        self.running = True
        
        # Start scheduler loop
        self.tasks.append(asyncio.create_task(self._scheduler_loop()))
        
        self.logger.info("Scheduler service started")
        
    async def stop(self):
        """Stop the scheduler service"""
        self.logger.info("Stopping scheduler service...")
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.logger.info("Scheduler service stopped")
        
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = datetime.now().time()
                
                # Check each scheduled task
                for task_name, schedule in self.schedules.items():
                    if not schedule['enabled']:
                        continue
                        
                    scheduled_time = schedule['time']
                    
                    # Check if it's time to run (within 1 minute window)
                    if self._is_time_to_run(current_time, scheduled_time):
                        self.logger.info(f"Running scheduled task: {task_name}")
                        
                        try:
                            await schedule['function']()
                            self.logger.info(f"Completed scheduled task: {task_name}")
                        except Exception as e:
                            self.logger.error(f"Error in scheduled task {task_name}: {e}")
                            
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                self.logger.error(f"Error in scheduler loop: {e}")
                await asyncio.sleep(60)
                
    def _is_time_to_run(self, current_time: time, scheduled_time: time) -> bool:
        """Check if current time matches scheduled time (within 1 minute)"""
        current_minutes = current_time.hour * 60 + current_time.minute
        scheduled_minutes = scheduled_time.hour * 60 + scheduled_time.minute
        
        # Within 1 minute window
        return abs(current_minutes - scheduled_minutes) <= 1
        
    async def run_nightly_optimization(self):
        """Run nightly strategy optimization"""
        try:
            self.logger.info("Starting nightly strategy optimization...")
            
            # Run the optimization
            await self.strategy_optimizer.start_nightly_optimization()
            
            self.logger.info("Nightly optimization completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error in nightly optimization: {e}")
            
    async def run_universe_update(self):
        """Run daily universe update"""
        try:
            self.logger.info("Starting daily universe update...")
            
            # Initialize if not already done
            if not self.universe_manager.universe:
                await self.universe_manager.initialize()
            
            # Run the update
            await self.universe_manager.update_universe_daily()
            
            self.logger.info("Universe update completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error in universe update: {e}")
            
    async def run_performance_check(self):
        """Run strategy performance check"""
        try:
            self.logger.info("Starting strategy performance check...")
            
            # This would implement performance monitoring
            # For now, just log that it ran
            self.logger.info("Strategy performance check completed")
            
        except Exception as e:
            self.logger.error(f"Error in performance check: {e}")
            
    def enable_task(self, task_name: str):
        """Enable a scheduled task"""
        if task_name in self.schedules:
            self.schedules[task_name]['enabled'] = True
            self.logger.info(f"Enabled scheduled task: {task_name}")
        else:
            self.logger.warning(f"Unknown task: {task_name}")
            
    def disable_task(self, task_name: str):
        """Disable a scheduled task"""
        if task_name in self.schedules:
            self.schedules[task_name]['enabled'] = False
            self.logger.info(f"Disabled scheduled task: {task_name}")
        else:
            self.logger.warning(f"Unknown task: {task_name}")
            
    def get_schedule_status(self) -> Dict[str, Any]:
        """Get status of all scheduled tasks"""
        status = {}
        
        for task_name, schedule in self.schedules.items():
            status[task_name] = {
                'enabled': schedule['enabled'],
                'scheduled_time': schedule['time'].strftime('%H:%M'),
                'next_run': self._calculate_next_run(schedule['time'])
            }
            
        return status
        
    def _calculate_next_run(self, scheduled_time: time) -> str:
        """Calculate when the task will next run"""
        now = datetime.now()
        today_run = datetime.combine(now.date(), scheduled_time)
        
        if today_run > now:
            next_run = today_run
        else:
            # Tomorrow
            from datetime import timedelta
            next_run = today_run + timedelta(days=1)
            
        return next_run.strftime('%Y-%m-%d %H:%M')
        
    async def run_task_now(self, task_name: str):
        """Manually trigger a scheduled task"""
        if task_name not in self.schedules:
            raise ValueError(f"Unknown task: {task_name}")
            
        self.logger.info(f"Manually running task: {task_name}")
        
        try:
            await self.schedules[task_name]['function']()
            self.logger.info(f"Manual task completed: {task_name}")
        except Exception as e:
            self.logger.error(f"Error in manual task {task_name}: {e}")
            raise
