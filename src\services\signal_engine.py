"""
Signal generation and strategy engine for Holly AI Trading System
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Set
from dataclasses import dataclass

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import (
    TradingSignal, SignalType, OrderSide, TimeFrame, 
    TechnicalIndicators, MarketData
)
from src.services.storage_service import StorageService
from src.services.feature_engine import FeatureEngine
from src.indicators.technical_indicators import TechnicalIndicatorEngine


@dataclass
class StrategyConfig:
    """Configuration for a trading strategy"""
    name: str
    signal_type: SignalType
    timeframes: List[TimeFrame]
    min_confidence: float
    enabled: bool = True
    
    # TTM Squeeze specific parameters
    bb_period: int = 20
    bb_stddev: float = 2.0
    kc_period: int = 20
    kc_multiplier: float = 1.5
    
    # Trend filter parameters
    use_trend_filter: bool = True
    ema_fast: int = 8
    ema_slow: int = 21
    
    # Volume filter parameters
    use_volume_filter: bool = True
    volume_ma_period: int = 20
    min_volume_ratio: float = 0.8


class SignalEngine:
    """Engine for generating trading signals"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.storage = StorageService()
        self.feature_engine = FeatureEngine(self.storage)
        self.indicator_engine = TechnicalIndicatorEngine()
        
        # Strategy configurations
        self.strategies: Dict[str, StrategyConfig] = {}
        self._initialize_default_strategies()
        
        # Active signals
        self.active_signals: Dict[str, TradingSignal] = {}
        
        # Scanning configuration
        self.scan_symbols: Set[str] = set()
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
    def _initialize_default_strategies(self):
        """Initialize default trading strategies"""
        
        # TTM Squeeze strategy
        self.strategies["ttm_squeeze"] = StrategyConfig(
            name="TTM Squeeze",
            signal_type=SignalType.TTM_SQUEEZE,
            timeframes=[TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30],
            min_confidence=0.7,
            bb_period=settings.TTM_BB_PERIOD,
            bb_stddev=settings.TTM_BB_STDDEV,
            kc_period=settings.TTM_KC_PERIOD,
            kc_multiplier=settings.TTM_KC_MULTIPLIER
        )
        
        # Momentum breakout strategy
        self.strategies["momentum_breakout"] = StrategyConfig(
            name="Momentum Breakout",
            signal_type=SignalType.MOMENTUM_BREAKOUT,
            timeframes=[TimeFrame.MINUTE_15, TimeFrame.MINUTE_30],
            min_confidence=0.6,
            use_trend_filter=True,
            use_volume_filter=True
        )
        
    async def start(self):
        """Start the signal engine"""
        self.logger.info("Starting signal engine...")
        self.running = True
        
        # Initialize storage and feature engine
        await self.storage.initialize()
        await self.feature_engine.start()
        
        # Start background tasks
        self.tasks.append(asyncio.create_task(self._signal_scanning_loop()))
        self.tasks.append(asyncio.create_task(self._signal_cleanup_loop()))
        
        self.logger.info("Signal engine started")
        
    async def stop(self):
        """Stop the signal engine"""
        self.logger.info("Stopping signal engine...")
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Stop feature engine
        await self.feature_engine.stop()
        
        # Close storage
        await self.storage.close()
        
        self.logger.info("Signal engine stopped")
        
    async def add_scan_symbol(self, symbol: str):
        """Add symbol to scanning list"""
        if symbol not in self.scan_symbols:
            self.scan_symbols.add(symbol)
            await self.feature_engine.subscribe_symbol(symbol)
            self.logger.info(f"Added {symbol} to signal scanning")
            
    async def remove_scan_symbol(self, symbol: str):
        """Remove symbol from scanning list"""
        if symbol in self.scan_symbols:
            self.scan_symbols.remove(symbol)
            await self.feature_engine.unsubscribe_symbol(symbol)
            self.logger.info(f"Removed {symbol} from signal scanning")
            
    async def get_live_signals(
        self, 
        timeframes: Optional[List[TimeFrame]] = None,
        top_n: Optional[int] = None,
        min_confidence: Optional[float] = None
    ) -> List[TradingSignal]:
        """Get current live trading signals"""
        try:
            signals = list(self.active_signals.values())
            
            # Filter by timeframes
            if timeframes:
                signals = [s for s in signals if s.timeframe in timeframes]
                
            # Filter by confidence
            if min_confidence:
                signals = [s for s in signals if s.confidence >= min_confidence]
                
            # Sort by confidence (highest first)
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
            # Limit results
            if top_n:
                signals = signals[:top_n]
                
            return signals
            
        except Exception as e:
            self.logger.error(f"Error getting live signals: {e}")
            return []
            
    async def get_signals_for_symbol(self, symbol: str) -> List[TradingSignal]:
        """Get active signals for a specific symbol"""
        return [signal for signal in self.active_signals.values() if signal.symbol == symbol]
        
    async def _signal_scanning_loop(self):
        """Main signal scanning loop"""
        while self.running:
            try:
                # Scan all symbols for signals
                for symbol in self.scan_symbols:
                    await self._scan_symbol_for_signals(symbol)
                    
                # Wait before next scan
                await asyncio.sleep(settings.SIGNAL_SCAN_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in signal scanning loop: {e}")
                await asyncio.sleep(60)
                
    async def _scan_symbol_for_signals(self, symbol: str):
        """Scan a symbol for trading signals"""
        try:
            # Check each strategy
            for strategy_name, strategy in self.strategies.items():
                if not strategy.enabled:
                    continue
                    
                # Check each timeframe for this strategy
                for timeframe in strategy.timeframes:
                    signal = await self._check_strategy_signal(symbol, timeframe, strategy)
                    
                    if signal and signal.confidence >= strategy.min_confidence:
                        # Add to active signals
                        self.active_signals[signal.id] = signal
                        
                        # Store signal
                        await self.storage.store_signal(signal.dict())
                        
                        self.logger.info(
                            f"Generated {strategy.signal_type.value} signal for {symbol} "
                            f"on {timeframe.value} with confidence {signal.confidence:.2f}"
                        )
                        
        except Exception as e:
            self.logger.error(f"Error scanning {symbol} for signals: {e}")
            
    async def _check_strategy_signal(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        strategy: StrategyConfig
    ) -> Optional[TradingSignal]:
        """Check if a strategy generates a signal for a symbol/timeframe"""
        try:
            # Get latest indicators
            indicators = await self.feature_engine.get_latest_indicators(symbol, timeframe)
            if not indicators:
                return None
                
            # Get indicators history for trend analysis
            indicators_history = await self.feature_engine.get_indicators_history(
                symbol, timeframe, limit=10
            )
            
            if len(indicators_history) < 3:
                return None
                
            # Check strategy-specific conditions
            if strategy.signal_type == SignalType.TTM_SQUEEZE:
                return await self._check_ttm_squeeze_signal(
                    symbol, timeframe, indicators, indicators_history, strategy
                )
            elif strategy.signal_type == SignalType.MOMENTUM_BREAKOUT:
                return await self._check_momentum_breakout_signal(
                    symbol, timeframe, indicators, indicators_history, strategy
                )
                
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking strategy signal: {e}")
            return None
            
    async def _check_ttm_squeeze_signal(
        self,
        symbol: str,
        timeframe: TimeFrame,
        indicators: TechnicalIndicators,
        history: List[TechnicalIndicators],
        strategy: StrategyConfig
    ) -> Optional[TradingSignal]:
        """Check for TTM Squeeze signals"""
        try:
            # Check if squeeze is firing (was on, now off)
            current = indicators
            previous = history[-2] if len(history) >= 2 else None
            
            if not previous:
                return None
                
            # Squeeze firing condition
            squeeze_firing = (
                previous.squeeze_on and 
                not current.squeeze_on and
                current.momentum is not None
            )
            
            if not squeeze_firing:
                return None
                
            # Determine direction based on momentum
            if current.momentum > 0:
                side = OrderSide.BUY
            else:
                side = OrderSide.SELL
                
            # Apply filters
            confidence = 0.5  # Base confidence
            
            # Trend filter
            if strategy.use_trend_filter and current.ema_8 and current.ema_21:
                if side == OrderSide.BUY and current.ema_8 > current.ema_21:
                    confidence += 0.2
                elif side == OrderSide.SELL and current.ema_8 < current.ema_21:
                    confidence += 0.2
                else:
                    confidence -= 0.1
                    
            # Volume filter
            if strategy.use_volume_filter and current.volume_ratio:
                if current.volume_ratio >= strategy.min_volume_ratio:
                    confidence += 0.1
                else:
                    confidence -= 0.1
                    
            # MACD confirmation
            if current.macd_histogram and len(history) >= 3:
                prev_hist = history[-2].macd_histogram
                if prev_hist:
                    if side == OrderSide.BUY and current.macd_histogram > prev_hist:
                        confidence += 0.1
                    elif side == OrderSide.SELL and current.macd_histogram < prev_hist:
                        confidence += 0.1
                        
            # Ensure confidence is within bounds
            confidence = max(0.0, min(1.0, confidence))
            
            if confidence < strategy.min_confidence:
                return None
                
            # Create signal
            signal = TradingSignal(
                id=str(uuid.uuid4()),
                symbol=symbol,
                timestamp=datetime.utcnow(),
                signal_type=strategy.signal_type,
                side=side,
                confidence=confidence,
                entry_price=Decimal("0"),  # Will be filled by execution service
                timeframe=timeframe,
                indicators=current,
                metadata={
                    "strategy": strategy.name,
                    "squeeze_firing": True,
                    "momentum": float(current.momentum) if current.momentum else None,
                    "trend_aligned": (
                        (side == OrderSide.BUY and current.ema_8 > current.ema_21) or
                        (side == OrderSide.SELL and current.ema_8 < current.ema_21)
                    ) if current.ema_8 and current.ema_21 else None
                }
            )
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error checking TTM squeeze signal: {e}")
            return None
            
    async def _check_momentum_breakout_signal(
        self,
        symbol: str,
        timeframe: TimeFrame,
        indicators: TechnicalIndicators,
        history: List[TechnicalIndicators],
        strategy: StrategyConfig
    ) -> Optional[TradingSignal]:
        """Check for momentum breakout signals"""
        try:
            # This is a placeholder for momentum breakout strategy
            # Would implement specific momentum breakout logic here
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking momentum breakout signal: {e}")
            return None
            
    async def _signal_cleanup_loop(self):
        """Clean up expired signals"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                expired_signals = []
                
                # Find expired signals (older than 1 hour)
                for signal_id, signal in self.active_signals.items():
                    if current_time - signal.timestamp > timedelta(hours=1):
                        expired_signals.append(signal_id)
                        
                # Remove expired signals
                for signal_id in expired_signals:
                    del self.active_signals[signal_id]
                    
                if expired_signals:
                    self.logger.info(f"Cleaned up {len(expired_signals)} expired signals")
                    
                # Wait before next cleanup
                await asyncio.sleep(300)  # Clean up every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in signal cleanup loop: {e}")
                await asyncio.sleep(300)
