#!/bin/bash

# Holly AI Trading System Startup Script

echo "🤖 Starting Holly AI Trading System..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure your API keys."
    echo "Required API keys:"
    echo "  - Alpaca Paper Trading API"
    echo "  - Financial Modeling Prep API"
    echo "  - OpenAI API"
    exit 1
fi

# Validate setup
echo "🔧 Validating setup..."
if command -v python3 &> /dev/null; then
    python3 validate_setup.py
elif command -v python &> /dev/null; then
    python validate_setup.py
else
    echo "⚠️  Python not found - skipping validation"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check backend API
if curl -f http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    echo "✅ Backend API is running"
else
    echo "⚠️  Backend API is not responding yet"
fi

# Check frontend
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ Frontend is running"
else
    echo "⚠️  Frontend is not responding yet"
fi

# Check Grafana
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Grafana is running"
else
    echo "⚠️  Grafana is not responding yet"
fi

echo ""
echo "🎉 Holly AI Trading System is ready!"
echo ""
echo "🤖 CHAT WITH HOLLY AI:"
echo "  • Web Interface: http://localhost:3001"
echo "  • Just type: 'Make me $50 today' and watch the magic happen!"
echo ""
echo "📊 Advanced Access:"
echo "  • Holly AI API: http://localhost:8080/api/v1/holly/chat"
echo "  • API Documentation: http://localhost:8080/docs"
echo "  • Grafana Dashboard: http://localhost:3000 (admin/admin)"
echo ""
echo "💬 Try these with Holly:"
echo "  • 'Make me $50 today'"
echo "  • 'What's AAPL looking like?'"
echo "  • 'Find me some momentum plays'"
echo "  • 'I need a hedge for my Tesla position'"
echo ""
echo "🛡️  SAFE PAPER TRADING MODE"
echo "   Holly uses simulated money for learning - no real trades!"
echo ""
echo "📚 Your API Keys Are Configured:"
echo "   ✅ Alpaca Paper Trading (PKI0KNC8...)"
echo "   ✅ Financial Modeling Prep (K63wnAbU...)"
echo "   ✅ OpenAI ChatGPT (sk-proj-9_2OZnc...)"
echo ""
echo "🚀 Ready to trade with Holly AI!"
echo ""
echo "🔧 To stop the system: docker-compose down"
echo "📋 To view logs: docker-compose logs -f"
echo ""

# Show container status
echo "📦 Container Status:"
docker-compose ps
