#!/bin/bash

# Holly AI Trading System Startup Script

echo "🤖 Starting Holly AI Trading System..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env and configure your API keys."
    echo "Required API keys:"
    echo "  - Alpaca Paper Trading API"
    echo "  - Financial Modeling Prep API"
    echo "  - OpenAI API"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check backend API
if curl -f http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    echo "✅ Backend API is running"
else
    echo "⚠️  Backend API is not responding yet"
fi

# Check frontend
if curl -f http://localhost:3001 > /dev/null 2>&1; then
    echo "✅ Frontend is running"
else
    echo "⚠️  Frontend is not responding yet"
fi

# Check Grafana
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Grafana is running"
else
    echo "⚠️  Grafana is not responding yet"
fi

echo ""
echo "🎉 Holly AI Trading System is starting up!"
echo ""
echo "📊 Access points:"
echo "  • Web Interface: http://localhost:3001"
echo "  • API Documentation: http://localhost:8080/docs"
echo "  • Grafana Dashboard: http://localhost:3000 (admin/admin)"
echo "  • Prometheus Metrics: http://localhost:8000"
echo ""
echo "💡 Try asking Holly: 'Make me $50 today'"
echo ""
echo "📝 Note: This system runs in PAPER TRADING mode only."
echo "   No real money will be used."
echo ""
echo "🔧 To stop the system: docker-compose down"
echo "📋 To view logs: docker-compose logs -f"
echo ""

# Show container status
echo "📦 Container Status:"
docker-compose ps
