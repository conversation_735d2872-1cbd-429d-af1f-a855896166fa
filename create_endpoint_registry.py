#!/usr/bin/env python3
"""
Create comprehensive endpoint registry for Holly AI
"""

import json

def create_alpaca_endpoints():
    """Define key Alpaca API endpoints"""
    return [
        # Account & Portfolio
        {"method": "GET", "path": "/v2/account", "description": "Get account information", "category": "account"},
        {"method": "GET", "path": "/v2/positions", "description": "Get all positions", "category": "positions"},
        {"method": "GET", "path": "/v2/positions/{symbol}", "description": "Get position for symbol", "category": "positions"},
        {"method": "DELETE", "path": "/v2/positions", "description": "Close all positions", "category": "positions"},
        {"method": "DELETE", "path": "/v2/positions/{symbol}", "description": "Close position", "category": "positions"},
        
        # Orders
        {"method": "POST", "path": "/v2/orders", "description": "Submit order", "category": "orders"},
        {"method": "GET", "path": "/v2/orders", "description": "Get orders", "category": "orders"},
        {"method": "GET", "path": "/v2/orders/{order_id}", "description": "Get order by ID", "category": "orders"},
        {"method": "PATCH", "path": "/v2/orders/{order_id}", "description": "Replace order", "category": "orders"},
        {"method": "DELETE", "path": "/v2/orders/{order_id}", "description": "Cancel order", "category": "orders"},
        {"method": "DELETE", "path": "/v2/orders", "description": "Cancel all orders", "category": "orders"},
        
        # Market Data
        {"method": "GET", "path": "/v2/stocks/{symbol}/bars", "description": "Get historical bars", "category": "market_data"},
        {"method": "GET", "path": "/v2/stocks/{symbol}/quotes", "description": "Get historical quotes", "category": "market_data"},
        {"method": "GET", "path": "/v2/stocks/{symbol}/trades", "description": "Get historical trades", "category": "market_data"},
        {"method": "GET", "path": "/v2/stocks/bars/latest", "description": "Get latest bars", "category": "market_data"},
        {"method": "GET", "path": "/v2/stocks/quotes/latest", "description": "Get latest quotes", "category": "market_data"},
        {"method": "GET", "path": "/v2/stocks/trades/latest", "description": "Get latest trades", "category": "market_data"},
        
        # Assets
        {"method": "GET", "path": "/v2/assets", "description": "Get assets", "category": "assets"},
        {"method": "GET", "path": "/v2/assets/{symbol}", "description": "Get asset by symbol", "category": "assets"},
        
        # Calendar & Clock
        {"method": "GET", "path": "/v2/calendar", "description": "Get market calendar", "category": "market_info"},
        {"method": "GET", "path": "/v2/clock", "description": "Get market clock", "category": "market_info"},
        
        # Portfolio History
        {"method": "GET", "path": "/v2/account/portfolio/history", "description": "Get portfolio history", "category": "account"},
        
        # Watchlists
        {"method": "GET", "path": "/v2/watchlists", "description": "Get watchlists", "category": "watchlists"},
        {"method": "POST", "path": "/v2/watchlists", "description": "Create watchlist", "category": "watchlists"},
        {"method": "GET", "path": "/v2/watchlists/{watchlist_id}", "description": "Get watchlist", "category": "watchlists"},
        {"method": "PUT", "path": "/v2/watchlists/{watchlist_id}", "description": "Update watchlist", "category": "watchlists"},
        {"method": "DELETE", "path": "/v2/watchlists/{watchlist_id}", "description": "Delete watchlist", "category": "watchlists"},
        {"method": "POST", "path": "/v2/watchlists/{watchlist_id}", "description": "Add asset to watchlist", "category": "watchlists"},
        {"method": "DELETE", "path": "/v2/watchlists/{watchlist_id}/{symbol}", "description": "Remove asset from watchlist", "category": "watchlists"},
    ]

def create_fmp_endpoints():
    """Define key FMP API endpoints"""
    return [
        # Company Information
        {"method": "GET", "path": "/v3/profile/{symbol}", "description": "Company profile", "category": "company"},
        {"method": "GET", "path": "/v3/quote/{symbol}", "description": "Real-time quote", "category": "quotes"},
        {"method": "GET", "path": "/v3/quote-short/{symbol}", "description": "Short quote", "category": "quotes"},
        {"method": "GET", "path": "/v3/quotes/{exchange}", "description": "All quotes by exchange", "category": "quotes"},
        
        # Financial Statements
        {"method": "GET", "path": "/v3/income-statement/{symbol}", "description": "Income statement", "category": "financials"},
        {"method": "GET", "path": "/v3/balance-sheet-statement/{symbol}", "description": "Balance sheet", "category": "financials"},
        {"method": "GET", "path": "/v3/cash-flow-statement/{symbol}", "description": "Cash flow statement", "category": "financials"},
        {"method": "GET", "path": "/v3/financial-statement-symbol-lists", "description": "Available symbols", "category": "financials"},
        
        # Ratios & Metrics
        {"method": "GET", "path": "/v3/ratios/{symbol}", "description": "Financial ratios", "category": "ratios"},
        {"method": "GET", "path": "/v3/key-metrics/{symbol}", "description": "Key metrics", "category": "ratios"},
        {"method": "GET", "path": "/v3/enterprise-values/{symbol}", "description": "Enterprise values", "category": "ratios"},
        {"method": "GET", "path": "/v3/financial-growth/{symbol}", "description": "Financial growth", "category": "ratios"},
        
        # Market Data
        {"method": "GET", "path": "/v3/historical-price-full/{symbol}", "description": "Historical prices", "category": "market_data"},
        {"method": "GET", "path": "/v3/historical-chart/{timeframe}/{symbol}", "description": "Historical chart data", "category": "market_data"},
        {"method": "GET", "path": "/v4/historical-price/{symbol}", "description": "Historical price v4", "category": "market_data"},
        
        # Screeners
        {"method": "GET", "path": "/v3/stock-screener", "description": "Stock screener", "category": "screener"},
        {"method": "GET", "path": "/v3/etf-screener", "description": "ETF screener", "category": "screener"},
        {"method": "GET", "path": "/v3/forex-screener", "description": "Forex screener", "category": "screener"},
        {"method": "GET", "path": "/v3/crypto-screener", "description": "Crypto screener", "category": "screener"},
        
        # News & Sentiment
        {"method": "GET", "path": "/v3/stock_news", "description": "Stock news", "category": "news"},
        {"method": "GET", "path": "/v4/stock_news", "description": "Stock news v4", "category": "news"},
        {"method": "GET", "path": "/v3/press-releases/{symbol}", "description": "Press releases", "category": "news"},
        {"method": "GET", "path": "/v4/general_news", "description": "General news", "category": "news"},
        
        # Insider Trading
        {"method": "GET", "path": "/v4/insider-trading", "description": "Insider trading", "category": "insider"},
        {"method": "GET", "path": "/v4/insider-trading-transaction-type", "description": "Insider transaction types", "category": "insider"},
        {"method": "GET", "path": "/v4/insider-roaster", "description": "Insider roster", "category": "insider"},
        {"method": "GET", "path": "/v4/insider-roaster-statistic", "description": "Insider statistics", "category": "insider"},
        
        # Institutional Holdings
        {"method": "GET", "path": "/v3/institutional-holder/{symbol}", "description": "Institutional holders", "category": "institutional"},
        {"method": "GET", "path": "/v3/mutual-fund-holder/{symbol}", "description": "Mutual fund holders", "category": "institutional"},
        {"method": "GET", "path": "/v3/etf-holder/{symbol}", "description": "ETF holders", "category": "institutional"},
        
        # Earnings & Events
        {"method": "GET", "path": "/v3/earning_calendar", "description": "Earnings calendar", "category": "events"},
        {"method": "GET", "path": "/v3/historical/earning_calendar/{symbol}", "description": "Historical earnings", "category": "events"},
        {"method": "GET", "path": "/v3/ipo_calendar", "description": "IPO calendar", "category": "events"},
        {"method": "GET", "path": "/v3/stock_dividend_calendar", "description": "Dividend calendar", "category": "events"},
        {"method": "GET", "path": "/v3/economic_calendar", "description": "Economic calendar", "category": "events"},
        
        # Technical Indicators
        {"method": "GET", "path": "/v3/technical_indicator/{timeframe}/{symbol}", "description": "Technical indicators", "category": "technical"},
        {"method": "GET", "path": "/v3/technical_indicator/daily/{symbol}", "description": "Daily technical indicators", "category": "technical"},
        
        # Market Indices
        {"method": "GET", "path": "/v3/quotes/index", "description": "Market indices", "category": "indices"},
        {"method": "GET", "path": "/v3/historical-price-full/index/{symbol}", "description": "Index historical data", "category": "indices"},
        
        # Commodities & Forex
        {"method": "GET", "path": "/v3/quotes/commodity", "description": "Commodity quotes", "category": "commodities"},
        {"method": "GET", "path": "/v3/quotes/forex", "description": "Forex quotes", "category": "forex"},
        {"method": "GET", "path": "/v3/historical-price-full/forex/{pair}", "description": "Forex historical data", "category": "forex"},
        
        # Crypto
        {"method": "GET", "path": "/v3/quotes/crypto", "description": "Crypto quotes", "category": "crypto"},
        {"method": "GET", "path": "/v3/historical-price-full/crypto/{symbol}", "description": "Crypto historical data", "category": "crypto"},
        
        # ETFs
        {"method": "GET", "path": "/v3/etf/list", "description": "ETF list", "category": "etf"},
        {"method": "GET", "path": "/v3/etf-stock-exposure/{symbol}", "description": "ETF stock exposure", "category": "etf"},
        {"method": "GET", "path": "/v3/etf-sector-weightings/{symbol}", "description": "ETF sector weightings", "category": "etf"},
        
        # Analyst Estimates
        {"method": "GET", "path": "/v3/analyst-estimates/{symbol}", "description": "Analyst estimates", "category": "estimates"},
        {"method": "GET", "path": "/v3/price-target/{symbol}", "description": "Price targets", "category": "estimates"},
        {"method": "GET", "path": "/v3/upgrades-downgrades", "description": "Upgrades/downgrades", "category": "estimates"},
        
        # Social Sentiment
        {"method": "GET", "path": "/v4/social-sentiment", "description": "Social sentiment", "category": "sentiment"},
        {"method": "GET", "path": "/v4/historical-social-sentiment", "description": "Historical social sentiment", "category": "sentiment"},
        
        # Market Performance
        {"method": "GET", "path": "/v3/gainers", "description": "Top gainers", "category": "performance"},
        {"method": "GET", "path": "/v3/losers", "description": "Top losers", "category": "performance"},
        {"method": "GET", "path": "/v3/actives", "description": "Most active", "category": "performance"},
        {"method": "GET", "path": "/v3/sectors-performance", "description": "Sector performance", "category": "performance"},
        
        # Company Search
        {"method": "GET", "path": "/v3/search", "description": "Company search", "category": "search"},
        {"method": "GET", "path": "/v3/search-ticker", "description": "Ticker search", "category": "search"},
        {"method": "GET", "path": "/v3/search-name", "description": "Company name search", "category": "search"},
    ]

def main():
    """Create comprehensive endpoint registry"""
    
    # Combine all endpoints
    all_endpoints = {
        "alpaca": create_alpaca_endpoints(),
        "fmp": create_fmp_endpoints()
    }
    
    # Save to JSON file
    with open("endpoint_registry.json", "w") as f:
        json.dump(all_endpoints, f, indent=2)
    
    # Print summary
    alpaca_count = len(all_endpoints["alpaca"])
    fmp_count = len(all_endpoints["fmp"])
    total_count = alpaca_count + fmp_count
    
    print(f"📊 Endpoint Registry Created!")
    print(f"   Alpaca endpoints: {alpaca_count}")
    print(f"   FMP endpoints: {fmp_count}")
    print(f"   Total endpoints: {total_count}")
    print(f"   Saved to: endpoint_registry.json")
    
    # Show categories
    alpaca_categories = set(ep["category"] for ep in all_endpoints["alpaca"])
    fmp_categories = set(ep["category"] for ep in all_endpoints["fmp"])
    
    print(f"\n📂 Alpaca Categories: {', '.join(sorted(alpaca_categories))}")
    print(f"📂 FMP Categories: {', '.join(sorted(fmp_categories))}")

if __name__ == "__main__":
    main()
