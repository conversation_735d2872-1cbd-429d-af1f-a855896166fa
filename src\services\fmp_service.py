"""
Financial Modeling Prep (FMP) API service
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp

from src.core.config import settings
from src.core.logging import get_logger


class FMPService:
    """Service for Financial Modeling Prep API integration"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.base_url = settings.FMP_BASE_URL
        self.api_key = settings.FMP_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            
    async def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """Make HTTP request to FMP API"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
                
            url = f"{self.base_url}/{endpoint}"
            
            # Add API key to parameters
            if not params:
                params = {}
            params['apikey'] = self.api_key
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    self.logger.error(f"FMP API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"Error making FMP request to {endpoint}: {e}")
            return None
            
    async def get_quote(self, symbol: str) -> Optional[Dict]:
        """Get real-time quote for a symbol"""
        endpoint = f"v3/quote/{symbol}"
        data = await self._make_request(endpoint)
        
        if data and isinstance(data, list) and len(data) > 0:
            return data[0]
        return None
        
    async def get_historical_data(
        self, 
        symbol: str, 
        from_date: datetime, 
        to_date: Optional[datetime] = None
    ) -> List[Dict]:
        """Get historical price data"""
        endpoint = f"v3/historical-price-full/{symbol}"
        
        params = {
            'from': from_date.strftime('%Y-%m-%d')
        }
        
        if to_date:
            params['to'] = to_date.strftime('%Y-%m-%d')
            
        data = await self._make_request(endpoint, params)
        
        if data and 'historical' in data:
            return data['historical']
        return []
        
    async def get_company_profile(self, symbol: str) -> Optional[Dict]:
        """Get company profile information"""
        endpoint = f"v3/profile/{symbol}"
        data = await self._make_request(endpoint)
        
        if data and isinstance(data, list) and len(data) > 0:
            return data[0]
        return None
        
    async def get_financial_statements(
        self, 
        symbol: str, 
        statement_type: str = "income-statement",
        period: str = "annual",
        limit: int = 5
    ) -> List[Dict]:
        """Get financial statements (income-statement, balance-sheet-statement, cash-flow-statement)"""
        endpoint = f"v3/{statement_type}/{symbol}"
        
        params = {
            'period': period,
            'limit': limit
        }
        
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_key_metrics(self, symbol: str, period: str = "annual", limit: int = 5) -> List[Dict]:
        """Get key financial metrics"""
        endpoint = f"v3/key-metrics/{symbol}"
        
        params = {
            'period': period,
            'limit': limit
        }
        
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_financial_ratios(self, symbol: str, period: str = "annual", limit: int = 5) -> List[Dict]:
        """Get financial ratios"""
        endpoint = f"v3/ratios/{symbol}"
        
        params = {
            'period': period,
            'limit': limit
        }
        
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_insider_trading(self, symbol: str, limit: int = 100) -> List[Dict]:
        """Get insider trading data"""
        endpoint = f"v4/insider-trading"
        
        params = {
            'symbol': symbol,
            'limit': limit
        }
        
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_stock_news(
        self, 
        symbol: Optional[str] = None, 
        limit: int = 50,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> List[Dict]:
        """Get stock news"""
        if symbol:
            endpoint = f"v3/stock_news"
            params = {
                'tickers': symbol,
                'limit': limit
            }
        else:
            endpoint = f"v3/stock_news"
            params = {
                'limit': limit
            }
            
        if from_date:
            params['from'] = from_date.strftime('%Y-%m-%d')
        if to_date:
            params['to'] = to_date.strftime('%Y-%m-%d')
            
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_earnings_calendar(
        self, 
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> List[Dict]:
        """Get earnings calendar"""
        endpoint = "v3/earning_calendar"
        
        params = {}
        if from_date:
            params['from'] = from_date.strftime('%Y-%m-%d')
        if to_date:
            params['to'] = to_date.strftime('%Y-%m-%d')
            
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_economic_calendar(
        self, 
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> List[Dict]:
        """Get economic calendar"""
        endpoint = "v3/economic_calendar"
        
        params = {}
        if from_date:
            params['from'] = from_date.strftime('%Y-%m-%d')
        if to_date:
            params['to'] = to_date.strftime('%Y-%m-%d')
            
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_market_hours(self) -> Optional[Dict]:
        """Get market hours and status"""
        endpoint = "v3/market-hours"
        return await self._make_request(endpoint)
        
    async def get_sector_performance(self) -> List[Dict]:
        """Get sector performance"""
        endpoint = "v3/sector-performance"
        data = await self._make_request(endpoint)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def search_stocks(self, query: str, limit: int = 10) -> List[Dict]:
        """Search for stocks"""
        endpoint = "v3/search"
        
        params = {
            'query': query,
            'limit': limit
        }
        
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
        
    async def get_stock_screener(
        self, 
        market_cap_more_than: Optional[int] = None,
        market_cap_lower_than: Optional[int] = None,
        price_more_than: Optional[float] = None,
        price_lower_than: Optional[float] = None,
        beta_more_than: Optional[float] = None,
        beta_lower_than: Optional[float] = None,
        volume_more_than: Optional[int] = None,
        volume_lower_than: Optional[int] = None,
        dividend_more_than: Optional[float] = None,
        dividend_lower_than: Optional[float] = None,
        is_etf: Optional[bool] = None,
        is_actively_trading: Optional[bool] = None,
        sector: Optional[str] = None,
        industry: Optional[str] = None,
        country: Optional[str] = None,
        exchange: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """Screen stocks based on criteria"""
        endpoint = "v3/stock-screener"
        
        params = {'limit': limit}
        
        # Add optional parameters
        if market_cap_more_than is not None:
            params['marketCapMoreThan'] = market_cap_more_than
        if market_cap_lower_than is not None:
            params['marketCapLowerThan'] = market_cap_lower_than
        if price_more_than is not None:
            params['priceMoreThan'] = price_more_than
        if price_lower_than is not None:
            params['priceLowerThan'] = price_lower_than
        if beta_more_than is not None:
            params['betaMoreThan'] = beta_more_than
        if beta_lower_than is not None:
            params['betaLowerThan'] = beta_lower_than
        if volume_more_than is not None:
            params['volumeMoreThan'] = volume_more_than
        if volume_lower_than is not None:
            params['volumeLowerThan'] = volume_lower_than
        if dividend_more_than is not None:
            params['dividendMoreThan'] = dividend_more_than
        if dividend_lower_than is not None:
            params['dividendLowerThan'] = dividend_lower_than
        if is_etf is not None:
            params['isEtf'] = str(is_etf).lower()
        if is_actively_trading is not None:
            params['isActivelyTrading'] = str(is_actively_trading).lower()
        if sector:
            params['sector'] = sector
        if industry:
            params['industry'] = industry
        if country:
            params['country'] = country
        if exchange:
            params['exchange'] = exchange
            
        data = await self._make_request(endpoint, params)
        
        if data and isinstance(data, list):
            return data
        return []
