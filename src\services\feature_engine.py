"""
Feature engine for calculating and managing technical indicators
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from collections import defaultdict, deque

from src.core.config import settings
from src.core.logging import get_logger
from src.indicators.technical_indicators import TechnicalIndicatorEngine
from src.models.trading import MarketData, TechnicalIndicators, TimeFrame
from src.services.storage_service import StorageService


class FeatureEngine:
    """Engine for calculating and caching technical indicators"""
    
    def __init__(self, storage_service: StorageService):
        self.logger = get_logger(__name__)
        self.storage = storage_service
        self.indicator_engine = TechnicalIndicatorEngine()
        
        # Cache for market data and indicators
        self.data_cache: Dict[str, Dict[TimeFrame, deque]] = defaultdict(lambda: defaultdict(deque))
        self.indicator_cache: Dict[str, Dict[TimeFrame, deque]] = defaultdict(lambda: defaultdict(deque))
        
        # Maximum cache size per symbol/timeframe
        self.max_cache_size = 1000
        
        # Subscribed symbols for real-time updates
        self.subscribed_symbols: Set[str] = set()
        
        # Background tasks
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
    async def start(self):
        """Start the feature engine"""
        self.logger.info("Starting feature engine...")
        self.running = True
        
        # Start background tasks
        self.tasks.append(asyncio.create_task(self._periodic_calculation()))
        
        self.logger.info("Feature engine started")
        
    async def stop(self):
        """Stop the feature engine"""
        self.logger.info("Stopping feature engine...")
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.logger.info("Feature engine stopped")
        
    async def subscribe_symbol(self, symbol: str):
        """Subscribe to feature calculations for a symbol"""
        if symbol not in self.subscribed_symbols:
            self.subscribed_symbols.add(symbol)
            
            # Initialize cache with historical data
            await self._initialize_symbol_cache(symbol)
            
            self.logger.info(f"Subscribed to features for {symbol}")
            
    async def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from feature calculations for a symbol"""
        if symbol in self.subscribed_symbols:
            self.subscribed_symbols.remove(symbol)
            
            # Clear cache
            if symbol in self.data_cache:
                del self.data_cache[symbol]
            if symbol in self.indicator_cache:
                del self.indicator_cache[symbol]
                
            self.logger.info(f"Unsubscribed from features for {symbol}")
            
    async def get_latest_indicators(
        self, 
        symbol: str, 
        timeframe: TimeFrame
    ) -> Optional[TechnicalIndicators]:
        """Get the latest technical indicators for a symbol"""
        try:
            # Check cache first
            if (symbol in self.indicator_cache and 
                timeframe in self.indicator_cache[symbol] and
                len(self.indicator_cache[symbol][timeframe]) > 0):
                return self.indicator_cache[symbol][timeframe][-1]
                
            # Calculate if not in cache
            await self._calculate_indicators_for_symbol(symbol, timeframe)
            
            # Return from cache
            if (symbol in self.indicator_cache and 
                timeframe in self.indicator_cache[symbol] and
                len(self.indicator_cache[symbol][timeframe]) > 0):
                return self.indicator_cache[symbol][timeframe][-1]
                
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting indicators for {symbol}: {e}")
            return None
            
    async def get_indicators_history(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        limit: int = 100
    ) -> List[TechnicalIndicators]:
        """Get historical indicators for a symbol"""
        try:
            if (symbol in self.indicator_cache and 
                timeframe in self.indicator_cache[symbol]):
                
                cache = self.indicator_cache[symbol][timeframe]
                return list(cache)[-limit:] if len(cache) >= limit else list(cache)
                
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting indicators history for {symbol}: {e}")
            return []
            
    async def update_market_data(self, data: MarketData):
        """Update market data and recalculate indicators"""
        try:
            symbol = data.symbol
            timeframe = data.timeframe
            
            # Add to cache
            if symbol not in self.data_cache:
                self.data_cache[symbol] = defaultdict(deque)
                
            cache = self.data_cache[symbol][timeframe]
            cache.append(data)
            
            # Maintain cache size
            if len(cache) > self.max_cache_size:
                cache.popleft()
                
            # Recalculate indicators if we have enough data
            if len(cache) >= 50:  # Minimum data points for indicators
                await self._calculate_indicators_for_symbol(symbol, timeframe)
                
        except Exception as e:
            self.logger.error(f"Error updating market data: {e}")
            
    async def _initialize_symbol_cache(self, symbol: str):
        """Initialize cache with historical data for a symbol"""
        try:
            # Get historical data for different timeframes
            timeframes = [TimeFrame.MINUTE_1, TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30]
            
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=30)  # Last 30 days
            
            for timeframe in timeframes:
                # Get data from storage
                data_records = await self.storage.get_market_data(
                    symbol=symbol,
                    timeframe=timeframe.value,
                    start=start_time,
                    end=end_time,
                    limit=500
                )
                
                # Convert to MarketData objects
                market_data = []
                for record in data_records:
                    if all(key in record for key in ['open', 'high', 'low', 'close', 'volume']):
                        data = MarketData(
                            symbol=symbol,
                            timestamp=record['timestamp'],
                            open=record['open'],
                            high=record['high'],
                            low=record['low'],
                            close=record['close'],
                            volume=record['volume'],
                            timeframe=timeframe
                        )
                        market_data.append(data)
                        
                # Add to cache
                if market_data:
                    self.data_cache[symbol][timeframe] = deque(market_data, maxlen=self.max_cache_size)
                    
                    # Calculate initial indicators
                    await self._calculate_indicators_for_symbol(symbol, timeframe)
                    
        except Exception as e:
            self.logger.error(f"Error initializing cache for {symbol}: {e}")
            
    async def _calculate_indicators_for_symbol(self, symbol: str, timeframe: TimeFrame):
        """Calculate indicators for a specific symbol and timeframe"""
        try:
            # Get market data from cache
            if (symbol not in self.data_cache or 
                timeframe not in self.data_cache[symbol] or
                len(self.data_cache[symbol][timeframe]) < 50):
                return
                
            market_data = list(self.data_cache[symbol][timeframe])
            
            # Calculate indicators
            indicators = self.indicator_engine.calculate_indicators(market_data, timeframe)
            
            if indicators:
                # Add to cache
                if symbol not in self.indicator_cache:
                    self.indicator_cache[symbol] = defaultdict(deque)
                    
                cache = self.indicator_cache[symbol][timeframe]
                cache.append(indicators)
                
                # Maintain cache size
                if len(cache) > self.max_cache_size:
                    cache.popleft()
                    
                # Store in database for persistence
                await self._store_indicators(indicators)
                
        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            
    async def _store_indicators(self, indicators: TechnicalIndicators):
        """Store indicators in database"""
        try:
            # Convert to dictionary for storage
            indicator_data = {
                'symbol': indicators.symbol,
                'timestamp': indicators.timestamp,
                'timeframe': indicators.timeframe.value,
                'bb_upper': float(indicators.bb_upper) if indicators.bb_upper else None,
                'bb_lower': float(indicators.bb_lower) if indicators.bb_lower else None,
                'kc_upper': float(indicators.kc_upper) if indicators.kc_upper else None,
                'kc_lower': float(indicators.kc_lower) if indicators.kc_lower else None,
                'squeeze_on': indicators.squeeze_on,
                'momentum': float(indicators.momentum) if indicators.momentum else None,
                'ema_8': float(indicators.ema_8) if indicators.ema_8 else None,
                'ema_21': float(indicators.ema_21) if indicators.ema_21 else None,
                'macd': float(indicators.macd) if indicators.macd else None,
                'macd_signal': float(indicators.macd_signal) if indicators.macd_signal else None,
                'macd_histogram': float(indicators.macd_histogram) if indicators.macd_histogram else None,
                'atr': float(indicators.atr) if indicators.atr else None,
                'volume_ma': float(indicators.volume_ma) if indicators.volume_ma else None,
                'volume_ratio': float(indicators.volume_ratio) if indicators.volume_ratio else None
            }
            
            # Store in cache for quick access
            cache_key = f"indicators:{indicators.symbol}:{indicators.timeframe.value}:latest"
            await self.storage.store_cache(cache_key, indicator_data, ttl=300)
            
        except Exception as e:
            self.logger.error(f"Error storing indicators: {e}")
            
    async def _periodic_calculation(self):
        """Periodically recalculate indicators for subscribed symbols"""
        while self.running:
            try:
                # Calculate indicators for all subscribed symbols
                for symbol in self.subscribed_symbols:
                    timeframes = [TimeFrame.MINUTE_1, TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30]
                    
                    for timeframe in timeframes:
                        await self._calculate_indicators_for_symbol(symbol, timeframe)
                        
                # Wait before next calculation
                await asyncio.sleep(60)  # Calculate every minute
                
            except Exception as e:
                self.logger.error(f"Error in periodic calculation: {e}")
                await asyncio.sleep(60)
