#!/usr/bin/env python3
"""
Quick test to verify API keys work
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_env_vars():
    """Test that environment variables are loaded"""
    print("🔧 Testing environment variables...")
    
    alpaca_key = os.getenv('APCA_API_KEY_ID')
    alpaca_secret = os.getenv('APCA_API_SECRET_KEY')
    fmp_key = os.getenv('FMP_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    print(f"✅ Alpaca Key: {alpaca_key[:8]}..." if alpaca_key else "❌ Alpaca Key: Missing")
    print(f"✅ Alpaca Secret: {alpaca_secret[:8]}..." if alpaca_secret else "❌ Alpaca Secret: Missing")
    print(f"✅ FMP Key: {fmp_key[:8]}..." if fmp_key else "❌ FMP Key: Missing")
    print(f"✅ OpenAI Key: {openai_key[:8]}..." if openai_key else "❌ OpenAI Key: Missing")
    
    return all([alpaca_key, alpaca_secret, fmp_key, openai_key])

def test_alpaca():
    """Test Alpaca connection"""
    print("\n🔑 Testing Alpaca API...")
    try:
        import alpaca_trade_api as tradeapi
        
        api = tradeapi.REST(
            key_id=os.getenv('APCA_API_KEY_ID'),
            secret_key=os.getenv('APCA_API_SECRET_KEY'),
            base_url=os.getenv('APCA_API_BASE_URL'),
            api_version='v2'
        )
        
        account = api.get_account()
        print(f"✅ Alpaca: Connected! Status: {account.status}")
        print(f"   Buying Power: ${float(account.buying_power):,.2f}")
        return True
    except Exception as e:
        print(f"❌ Alpaca: {e}")
        return False

def test_fmp():
    """Test FMP API"""
    print("\n🔑 Testing FMP API...")
    try:
        import requests
        
        api_key = os.getenv('FMP_API_KEY')
        url = f"https://financialmodelingprep.com/api/v3/quote/AAPL?apikey={api_key}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                price = data[0].get('price', 'N/A')
                print(f"✅ FMP: Connected! AAPL: ${price}")
                return True
        
        print(f"❌ FMP: HTTP {response.status_code}")
        return False
    except Exception as e:
        print(f"❌ FMP: {e}")
        return False

def test_openai():
    """Test OpenAI API"""
    print("\n🔑 Testing OpenAI API...")
    try:
        from openai import OpenAI
        
        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": "Say 'Holly AI ready!'"}],
            max_tokens=10
        )
        
        message = response.choices[0].message.content
        print(f"✅ OpenAI: Connected! Response: {message}")
        return True
    except Exception as e:
        print(f"❌ OpenAI: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Holly AI - Quick API Test\n")
    
    # Test environment variables
    if not test_env_vars():
        print("\n❌ Environment variables missing!")
        exit(1)
    
    # Test APIs
    results = []
    results.append(test_alpaca())
    results.append(test_fmp())
    results.append(test_openai())
    
    print("\n" + "="*40)
    if all(results):
        print("🎉 ALL SYSTEMS GO!")
        print("Holly AI is ready to trade!")
        print("\nNext steps:")
        print("1. Run: ./start.sh")
        print("2. Visit: http://localhost:3001")
        print("3. Chat: 'Make me $50 today'")
    else:
        print("❌ Some tests failed")
        print("Check your API keys in .env file")
