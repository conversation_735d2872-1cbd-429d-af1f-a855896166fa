"""
Configuration management for Holly AI Trading System
"""

import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    PORT: int = Field(default=8080, env="PORT")
    
    # Alpaca API
    APCA_API_BASE_URL: str = Field(env="APCA_API_BASE_URL")
    APCA_API_KEY_ID: str = Field(env="APCA_API_KEY_ID")
    APCA_API_SECRET_KEY: str = Field(env="APCA_API_SECRET_KEY")
    
    # Financial Modeling Prep
    FMP_API_KEY: str = Field(env="FMP_API_KEY")
    FMP_BASE_URL: str = Field(default="https://financialmodelingprep.com/api", env="FMP_BASE_URL")
    
    # OpenAI
    OPENAI_API_KEY: str = Field(env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="gpt-4", env="OPENAI_MODEL")
    OPENAI_TEMPERATURE: float = Field(default=0.2, env="OPENAI_TEMPERATURE")
    
    # Redis
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # InfluxDB
    INFLUXDB_URL: str = Field(default="http://localhost:8086", env="INFLUXDB_URL")
    INFLUXDB_TOKEN: str = Field(env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(default="holly-ai", env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(default="market-data", env="INFLUXDB_BUCKET")
    
    # Trading Configuration
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")
    DEFAULT_RISK_PERCENT: float = Field(default=2.0, env="DEFAULT_RISK_PERCENT")
    MAX_POSITIONS: int = Field(default=10, env="MAX_POSITIONS")
    MAX_POSITION_SIZE: float = Field(default=10000.0, env="MAX_POSITION_SIZE")
    
    # Signal Engine
    SIGNAL_SCAN_INTERVAL: int = Field(default=60, env="SIGNAL_SCAN_INTERVAL")  # seconds
    BACKTEST_LOOKBACK_DAYS: int = Field(default=252, env="BACKTEST_LOOKBACK_DAYS")
    
    # TTM Squeeze Parameters
    TTM_BB_PERIOD: int = Field(default=20, env="TTM_BB_PERIOD")
    TTM_BB_STDDEV: float = Field(default=2.0, env="TTM_BB_STDDEV")
    TTM_KC_PERIOD: int = Field(default=20, env="TTM_KC_PERIOD")
    TTM_KC_MULTIPLIER: float = Field(default=1.5, env="TTM_KC_MULTIPLIER")
    
    # Stop Loss Configuration
    DEFAULT_ATR_MULTIPLIER: float = Field(default=1.5, env="DEFAULT_ATR_MULTIPLIER")
    MIN_STOP_DISTANCE: float = Field(default=0.01, env="MIN_STOP_DISTANCE")  # 1%
    
    # Monitoring
    PROMETHEUS_PORT: int = Field(default=8000, env="PROMETHEUS_PORT")

    # Signal Engine Configuration
    MIN_SIGNAL_CONFIDENCE: float = Field(default=0.6, env="MIN_SIGNAL_CONFIDENCE")

    # Holly AI Configuration
    HOLLY_MAX_CONVERSATION_HISTORY: int = Field(default=100, env="HOLLY_MAX_CONVERSATION_HISTORY")
    HOLLY_FUNCTION_TIMEOUT: int = Field(default=30, env="HOLLY_FUNCTION_TIMEOUT")

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
