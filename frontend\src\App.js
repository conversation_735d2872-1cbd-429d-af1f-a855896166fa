import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  AppBar,
  Toolbar,
  Card,
  CardContent
} from '@mui/material';
import HollyChat from './components/HollyChat';
import Dashboard from './components/Dashboard';
import SignalsList from './components/SignalsList';
import PositionsList from './components/PositionsList';
import AccountInfo from './components/AccountInfo';
import './App.css';

function App() {
  const [accountData, setAccountData] = useState(null);
  const [positions, setPositions] = useState([]);
  const [signals, setSignals] = useState([]);

  useEffect(() => {
    // Fetch initial data
    fetchAccountData();
    fetchPositions();
    fetchSignals();

    // Set up periodic updates
    const interval = setInterval(() => {
      fetchAccountData();
      fetchPositions();
      fetchSignals();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchAccountData = async () => {
    try {
      const response = await fetch('/api/v1/account');
      if (response.ok) {
        const data = await response.json();
        setAccountData(data);
      }
    } catch (error) {
      console.error('Error fetching account data:', error);
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/v1/positions');
      if (response.ok) {
        const data = await response.json();
        setPositions(data);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
    }
  };

  const fetchSignals = async () => {
    try {
      const response = await fetch('/api/v1/signals');
      if (response.ok) {
        const data = await response.json();
        setSignals(data);
      }
    } catch (error) {
      console.error('Error fetching signals:', error);
    }
  };

  return (
    <div className="App">
      <AppBar position="static" sx={{ backgroundColor: '#1976d2' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            🤖 Holly AI Trading System
          </Typography>
          <Typography variant="body2" sx={{
            backgroundColor: 'rgba(255,255,255,0.2)',
            px: 1,
            py: 0.5,
            borderRadius: 1
          }}>
            Paper Trading Mode
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 2 }}>
        <Grid container spacing={3}>
          {/* Account Info */}
          <Grid item xs={12} md={3}>
            <AccountInfo accountData={accountData} />
          </Grid>

          {/* Quick Dashboard */}
          <Grid item xs={12} md={3}>
            <Dashboard
              accountData={accountData}
              positions={positions}
              signals={signals}
            />
          </Grid>

          {/* Holly AI Chat Interface */}
          <Grid item xs={12} md={6}>
            <HollyChat onPlanExecuted={() => {
              fetchAccountData();
              fetchPositions();
              fetchSignals();
            }} />
          </Grid>

          {/* Active Signals */}
          <Grid item xs={12} md={6}>
            <SignalsList signals={signals} />
          </Grid>

          {/* Current Positions */}
          <Grid item xs={12} md={6}>
            <PositionsList positions={positions} />
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}

export default App;
