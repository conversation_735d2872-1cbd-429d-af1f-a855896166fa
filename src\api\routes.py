"""
API routes for Holly AI Trading System
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.services.trading_orchestrator import TradingOrchestrator
from src.services.signal_engine import SignalEngine
from src.services.execution_service import ExecutionService
from src.services.data_ingestion import DataIngestionService
from src.models.trading import TradingGoal, Order, OrderSide, OrderType

# Create router
api_router = APIRouter()

# Global service instances (will be injected)
orchestrator = TradingOrchestrator()
signal_engine = SignalEngine()
execution_service = ExecutionService()
data_service = DataIngestionService()


# Request/Response models
class ChatRequest(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    response: str
    trading_plan: Optional[Dict[str, Any]] = None
    plan_id: Optional[str] = None
    error: bool = False


class ExecutePlanRequest(BaseModel):
    plan_id: str


class OrderRequest(BaseModel):
    symbol: str
    qty: float
    side: OrderSide
    order_type: OrderType
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None


class SymbolRequest(BaseModel):
    symbol: str


# Chat endpoint
@api_router.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint for user interactions"""
    try:
        result = await orchestrator.process_user_request(request.message)
        
        return ChatResponse(
            response=result.get('response', ''),
            trading_plan=result.get('trading_plan'),
            plan_id=result.get('plan_id'),
            error=result.get('error', False)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Execute trading plan
@api_router.post("/execute-plan")
async def execute_plan(request: ExecutePlanRequest):
    """Execute a trading plan"""
    try:
        result = await orchestrator.execute_trading_plan(request.plan_id)
        
        if result.get('error'):
            raise HTTPException(status_code=400, detail=result['error'])
            
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get live signals
@api_router.get("/signals")
async def get_signals(
    timeframes: Optional[str] = None,
    top_n: Optional[int] = 10,
    min_confidence: Optional[float] = 0.6
):
    """Get live trading signals"""
    try:
        # Parse timeframes if provided
        tf_list = None
        if timeframes:
            from src.models.trading import TimeFrame
            tf_list = [TimeFrame(tf.strip()) for tf in timeframes.split(',')]
            
        signals = await signal_engine.get_live_signals(
            timeframes=tf_list,
            top_n=top_n,
            min_confidence=min_confidence
        )
        
        return [signal.dict() for signal in signals]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get signals for specific symbol
@api_router.get("/signals/{symbol}")
async def get_signals_for_symbol(symbol: str):
    """Get signals for a specific symbol"""
    try:
        signals = await signal_engine.get_signals_for_symbol(symbol.upper())
        return [signal.dict() for signal in signals]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Submit order manually
@api_router.post("/orders")
async def submit_order(request: OrderRequest):
    """Submit a trading order"""
    try:
        order = Order(
            symbol=request.symbol.upper(),
            qty=request.qty,
            side=request.side,
            order_type=request.order_type,
            limit_price=request.limit_price,
            stop_price=request.stop_price
        )
        
        order_id = await execution_service.submit_order(order)
        
        if not order_id:
            raise HTTPException(status_code=400, detail="Failed to submit order")
            
        return {"order_id": order_id, "status": "submitted"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get order status
@api_router.get("/orders/{order_id}")
async def get_order_status(order_id: str):
    """Get order status"""
    try:
        status = await execution_service.get_order_status(order_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="Order not found")
            
        return {"order_id": order_id, "status": status.value}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Cancel order
@api_router.delete("/orders/{order_id}")
async def cancel_order(order_id: str):
    """Cancel an order"""
    try:
        success = await execution_service.cancel_order(order_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to cancel order")
            
        return {"order_id": order_id, "status": "canceled"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get positions
@api_router.get("/positions")
async def get_positions():
    """Get current positions"""
    try:
        positions = await execution_service.get_positions()
        return [position.dict() for position in positions]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get account info
@api_router.get("/account")
async def get_account():
    """Get account information"""
    try:
        account_info = await execution_service.get_account_info()
        
        if not account_info:
            raise HTTPException(status_code=500, detail="Failed to get account info")
            
        return account_info
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get quote
@api_router.get("/quote/{symbol}")
async def get_quote(symbol: str):
    """Get current quote for a symbol"""
    try:
        quote = await data_service.get_latest_quote(symbol.upper())
        
        if not quote:
            raise HTTPException(status_code=404, detail="Quote not found")
            
        return quote
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Subscribe to symbol
@api_router.post("/subscribe")
async def subscribe_symbol(request: SymbolRequest):
    """Subscribe to real-time data for a symbol"""
    try:
        symbol = request.symbol.upper()
        
        # Subscribe to data and signals
        await data_service.subscribe_symbol(symbol)
        await signal_engine.add_scan_symbol(symbol)
        
        return {"symbol": symbol, "status": "subscribed"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Unsubscribe from symbol
@api_router.delete("/subscribe/{symbol}")
async def unsubscribe_symbol(symbol: str):
    """Unsubscribe from real-time data for a symbol"""
    try:
        symbol = symbol.upper()
        
        # Unsubscribe from data and signals
        await data_service.unsubscribe_symbol(symbol)
        await signal_engine.remove_scan_symbol(symbol)
        
        return {"symbol": symbol, "status": "unsubscribed"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Universe management
@api_router.get("/universe")
async def get_universe():
    """Get active trading universe"""
    try:
        from src.services.universe_manager import UniverseManager
        universe_manager = UniverseManager()
        await universe_manager.initialize()

        active_symbols = universe_manager.get_active_symbols()
        return {"active_symbols": active_symbols, "count": len(active_symbols)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/universe/sectors/{sector}")
async def get_symbols_by_sector(sector: str):
    """Get symbols by sector"""
    try:
        from src.services.universe_manager import UniverseManager
        universe_manager = UniverseManager()
        await universe_manager.initialize()

        symbols = universe_manager.get_symbols_by_sector(sector)
        return {"sector": sector, "symbols": symbols}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/universe/screen")
async def screen_universe(
    min_market_cap: Optional[float] = None,
    max_market_cap: Optional[float] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    min_volume: Optional[float] = None,
    sectors: Optional[str] = None,
    limit: int = 50
):
    """Screen universe with criteria"""
    try:
        from src.services.universe_manager import UniverseManager
        universe_manager = UniverseManager()
        await universe_manager.initialize()

        sector_list = sectors.split(',') if sectors else None

        symbols = universe_manager.get_symbols_by_criteria(
            min_market_cap=min_market_cap,
            max_market_cap=max_market_cap,
            min_price=min_price,
            max_price=max_price,
            min_volume=min_volume,
            sectors=sector_list,
            limit=limit
        )

        return {"symbols": symbols, "count": len(symbols)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/scheduler/status")
async def get_scheduler_status():
    """Get scheduler status"""
    try:
        from src.services.scheduler_service import SchedulerService
        scheduler = SchedulerService()

        status = scheduler.get_schedule_status()
        return status

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/scheduler/run/{task_name}")
async def run_scheduled_task(task_name: str):
    """Manually run a scheduled task"""
    try:
        from src.services.scheduler_service import SchedulerService
        scheduler = SchedulerService()

        await scheduler.run_task_now(task_name)
        return {"message": f"Task {task_name} completed successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Health check
@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "signal_engine": signal_engine.running,
            "execution_service": execution_service.running,
            "data_service": data_service.running
        }
    }


# Get system status
@api_router.get("/status")
async def get_status():
    """Get detailed system status"""
    try:
        # Get active signals count
        signals = await signal_engine.get_live_signals()
        
        # Get positions
        positions = await execution_service.get_positions()
        
        # Get account info
        account = await execution_service.get_account_info()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "active_signals": len(signals),
            "active_positions": len(positions),
            "scan_symbols": list(signal_engine.scan_symbols),
            "account_status": account.get('status') if account else None,
            "buying_power": account.get('buying_power') if account else None,
            "portfolio_value": account.get('portfolio_value') if account else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
