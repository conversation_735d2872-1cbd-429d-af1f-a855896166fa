"""
Universal API Gateway - Gives Holly AI access to ALL Alpaca and FMP endpoints
This is the comprehensive API layer that <PERSON> can call for any market data need
"""

import json
import logging
import aiohttp
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

class UniversalAPIGateway:
    """
    Universal gateway providing access to all Alpaca and FMP endpoints
    Holly AI can call any endpoint through this gateway
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # API configurations
        self.alpaca_config = {
            "base_url": os.getenv("APCA_API_BASE_URL", "https://paper-api.alpaca.markets"),
            "key_id": os.getenv("APCA_API_KEY_ID"),
            "secret_key": os.getenv("APCA_API_SECRET_KEY"),
            "headers": {
                "APCA-API-KEY-ID": os.getenv("APCA_API_KEY_ID"),
                "APCA-API-SECRET-KEY": os.getenv("APCA_API_SECRET_KEY"),
                "Content-Type": "application/json"
            }
        }
        
        self.fmp_config = {
            "base_url": os.getenv("FMP_BASE_URL", "https://financialmodelingprep.com/api"),
            "api_key": os.getenv("FMP_API_KEY"),
        }
        
        # Load endpoint registry
        self.endpoints = self._load_endpoint_registry()
        
    def _load_endpoint_registry(self) -> Dict:
        """Load the comprehensive endpoint registry"""
        try:
            with open("endpoint_registry.json", "r") as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning("Endpoint registry not found, using minimal set")
            return {"alpaca": [], "fmp": []}
    
    async def call_alpaca_endpoint(
        self, 
        method: str, 
        path: str, 
        params: Optional[Dict] = None,
        data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Call any Alpaca API endpoint
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE, PATCH)
            path: API path (e.g., "/v2/account", "/v2/orders")
            params: Query parameters
            data: Request body data
            
        Returns:
            API response as dictionary
        """
        try:
            url = f"{self.alpaca_config['base_url']}{path}"
            
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=method.upper(),
                    url=url,
                    headers=self.alpaca_config["headers"],
                    params=params,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "data": result,
                            "endpoint": f"{method.upper()} {path}",
                            "api": "alpaca"
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "endpoint": f"{method.upper()} {path}",
                            "api": "alpaca"
                        }
                        
        except Exception as e:
            self.logger.error(f"Alpaca API call failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "endpoint": f"{method.upper()} {path}",
                "api": "alpaca"
            }
    
    async def call_fmp_endpoint(
        self, 
        path: str, 
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Call any FMP API endpoint
        
        Args:
            path: API path (e.g., "/v3/quote/AAPL", "/v3/profile/MSFT")
            params: Additional query parameters
            
        Returns:
            API response as dictionary
        """
        try:
            # Add API key to parameters
            if params is None:
                params = {}
            params["apikey"] = self.fmp_config["api_key"]
            
            url = f"{self.fmp_config['base_url']}{path}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url=url,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return {
                            "success": True,
                            "data": result,
                            "endpoint": f"GET {path}",
                            "api": "fmp"
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "endpoint": f"GET {path}",
                            "api": "fmp"
                        }
                        
        except Exception as e:
            self.logger.error(f"FMP API call failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "endpoint": f"GET {path}",
                "api": "fmp"
            }
    
    async def universal_api_call(
        self, 
        api: str, 
        endpoint_name: str, 
        path_params: Optional[Dict] = None,
        query_params: Optional[Dict] = None,
        body_data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Universal API call function for Holly AI
        
        Args:
            api: "alpaca" or "fmp"
            endpoint_name: Name/description of endpoint to call
            path_params: Parameters to substitute in path (e.g., {"symbol": "AAPL"})
            query_params: Query parameters
            body_data: Request body (for POST/PUT requests)
            
        Returns:
            API response
        """
        try:
            # Find matching endpoint
            endpoints = self.endpoints.get(api.lower(), [])
            matching_endpoint = None
            
            for endpoint in endpoints:
                if (endpoint_name.lower() in endpoint["description"].lower() or 
                    endpoint_name.lower() in endpoint["path"].lower()):
                    matching_endpoint = endpoint
                    break
            
            if not matching_endpoint:
                return {
                    "success": False,
                    "error": f"Endpoint '{endpoint_name}' not found for {api}",
                    "available_endpoints": [ep["description"] for ep in endpoints[:10]]
                }
            
            # Substitute path parameters
            path = matching_endpoint["path"]
            if path_params:
                for key, value in path_params.items():
                    path = path.replace(f"{{{key}}}", str(value))
            
            # Make the API call
            if api.lower() == "alpaca":
                return await self.call_alpaca_endpoint(
                    method=matching_endpoint["method"],
                    path=path,
                    params=query_params,
                    data=body_data
                )
            elif api.lower() == "fmp":
                return await self.call_fmp_endpoint(
                    path=path,
                    params=query_params
                )
            else:
                return {
                    "success": False,
                    "error": f"Unknown API: {api}"
                }
                
        except Exception as e:
            self.logger.error(f"Universal API call failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_available_endpoints(self, api: Optional[str] = None) -> Dict[str, Any]:
        """Get list of available endpoints"""
        if api:
            endpoints = self.endpoints.get(api.lower(), [])
            return {
                "api": api,
                "count": len(endpoints),
                "endpoints": endpoints
            }
        else:
            return {
                "alpaca": {
                    "count": len(self.endpoints.get("alpaca", [])),
                    "categories": list(set(ep["category"] for ep in self.endpoints.get("alpaca", [])))
                },
                "fmp": {
                    "count": len(self.endpoints.get("fmp", [])),
                    "categories": list(set(ep["category"] for ep in self.endpoints.get("fmp", [])))
                },
                "total_endpoints": len(self.endpoints.get("alpaca", [])) + len(self.endpoints.get("fmp", []))
            }
    
    def search_endpoints(self, query: str) -> List[Dict]:
        """Search for endpoints by description or path"""
        results = []
        query_lower = query.lower()
        
        for api_name, endpoints in self.endpoints.items():
            for endpoint in endpoints:
                if (query_lower in endpoint["description"].lower() or 
                    query_lower in endpoint["path"].lower() or
                    query_lower in endpoint["category"].lower()):
                    results.append({
                        "api": api_name,
                        **endpoint
                    })
        
        return results[:20]  # Limit to top 20 results
    
    # Convenience methods for common operations
    async def get_account_info(self) -> Dict[str, Any]:
        """Get Alpaca account information"""
        return await self.call_alpaca_endpoint("GET", "/v2/account")
    
    async def get_positions(self) -> Dict[str, Any]:
        """Get all positions"""
        return await self.call_alpaca_endpoint("GET", "/v2/positions")
    
    async def get_orders(self, status: Optional[str] = None) -> Dict[str, Any]:
        """Get orders"""
        params = {"status": status} if status else None
        return await self.call_alpaca_endpoint("GET", "/v2/orders", params=params)
    
    async def get_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote from FMP"""
        return await self.call_fmp_endpoint(f"/v3/quote/{symbol.upper()}")
    
    async def get_company_profile(self, symbol: str) -> Dict[str, Any]:
        """Get company profile from FMP"""
        return await self.call_fmp_endpoint(f"/v3/profile/{symbol.upper()}")
    
    async def get_financial_statements(self, symbol: str, statement_type: str = "income-statement") -> Dict[str, Any]:
        """Get financial statements from FMP"""
        return await self.call_fmp_endpoint(f"/v3/{statement_type}/{symbol.upper()}")
    
    async def get_stock_news(self, symbol: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """Get stock news from FMP"""
        params = {"limit": limit}
        if symbol:
            params["tickers"] = symbol.upper()
        return await self.call_fmp_endpoint("/v3/stock_news", params=params)
    
    async def screen_stocks(self, **criteria) -> Dict[str, Any]:
        """Screen stocks using FMP screener"""
        return await self.call_fmp_endpoint("/v3/stock-screener", params=criteria)
    
    async def get_market_gainers(self) -> Dict[str, Any]:
        """Get top market gainers"""
        return await self.call_fmp_endpoint("/v3/gainers")
    
    async def get_market_losers(self) -> Dict[str, Any]:
        """Get top market losers"""
        return await self.call_fmp_endpoint("/v3/losers")
    
    async def get_most_active(self) -> Dict[str, Any]:
        """Get most active stocks"""
        return await self.call_fmp_endpoint("/v3/actives")
