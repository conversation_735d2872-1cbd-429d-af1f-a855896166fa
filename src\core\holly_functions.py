"""
Holly AI Function Implementations
All the actual trading functions that <PERSON> can call
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
import numpy as np

from src.core.logging import get_logger


class HollyFunctions:
    """Implementation of all <PERSON>'s trading functions"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
    async def interpret_trading_goal(
        self, 
        user_request: str,
        profit_target: Optional[float] = None,
        timeframe: Optional[str] = None,
        risk_tolerance: Optional[str] = None,
        hedging_requested: Optional[bool] = None,
        specific_symbols: Optional[List[str]] = None,
        strategy_preference: Optional[str] = None
    ) -> Dict[str, Any]:
        """Interpret and structure a trading goal from natural language"""
        try:
            # Extract profit target from request if not provided
            if not profit_target:
                # Simple extraction - in real implementation, use more sophisticated NLP
                import re
                money_matches = re.findall(r'\$(\d+(?:\.\d{2})?)', user_request)
                if money_matches:
                    profit_target = float(money_matches[0])
                else:
                    profit_target = 50.0  # Default
                    
            # Determine timeframe from request
            if not timeframe:
                if any(word in user_request.lower() for word in ['today', 'intraday', 'scalp']):
                    timeframe = 'intraday'
                elif any(word in user_request.lower() for word in ['swing', 'few days', 'week']):
                    timeframe = 'swing'
                else:
                    timeframe = 'intraday'
                    
            # Determine risk tolerance
            if not risk_tolerance:
                if any(word in user_request.lower() for word in ['safe', 'conservative', 'low risk']):
                    risk_tolerance = 'conservative'
                elif any(word in user_request.lower() for word in ['aggressive', 'high risk', 'yolo']):
                    risk_tolerance = 'aggressive'
                else:
                    risk_tolerance = 'moderate'
                    
            # Check for hedging request
            if hedging_requested is None:
                hedging_requested = any(word in user_request.lower() for word in ['hedge', 'protect', 'insurance'])
                
            # Extract symbols mentioned
            if not specific_symbols:
                import re
                # Look for stock symbols (3-5 uppercase letters)
                symbol_matches = re.findall(r'\b[A-Z]{2,5}\b', user_request)
                specific_symbols = symbol_matches if symbol_matches else None
                
            # Determine strategy preference
            if not strategy_preference:
                if any(word in user_request.lower() for word in ['momentum', 'breakout', 'squeeze']):
                    strategy_preference = 'momentum'
                elif any(word in user_request.lower() for word in ['mean reversion', 'oversold', 'bounce']):
                    strategy_preference = 'mean_reversion'
                else:
                    strategy_preference = 'any'
                    
            return {
                "success": True,
                "goal": {
                    "original_request": user_request,
                    "profit_target": profit_target,
                    "timeframe": timeframe,
                    "risk_tolerance": risk_tolerance,
                    "hedging_requested": hedging_requested,
                    "specific_symbols": specific_symbols,
                    "strategy_preference": strategy_preference,
                    "max_risk_percent": 1.0 if risk_tolerance == 'conservative' else 2.0 if risk_tolerance == 'moderate' else 3.0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error interpreting trading goal: {e}")
            return {"success": False, "error": str(e)}
            
    async def get_real_time_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote data"""
        try:
            from src.services.data_ingestion import DataIngestionService
            
            data_service = DataIngestionService()
            quote = await data_service.get_latest_quote(symbol.upper())
            
            if quote:
                return {
                    "success": True,
                    "symbol": symbol.upper(),
                    "price": quote.get('bid', 0),  # Use bid as current price
                    "bid": quote.get('bid', 0),
                    "ask": quote.get('ask', 0),
                    "volume": quote.get('volume', 0),
                    "timestamp": quote.get('timestamp', datetime.utcnow().isoformat())
                }
            else:
                # Fallback to FMP
                from src.services.fmp_service import FMPService
                async with FMPService() as fmp:
                    fmp_quote = await fmp.get_quote(symbol.upper())
                    if fmp_quote:
                        return {
                            "success": True,
                            "symbol": symbol.upper(),
                            "price": fmp_quote.get('price', 0),
                            "bid": fmp_quote.get('price', 0),
                            "ask": fmp_quote.get('price', 0),
                            "volume": fmp_quote.get('volume', 0),
                            "change": fmp_quote.get('change', 0),
                            "change_percent": fmp_quote.get('changesPercentage', 0),
                            "timestamp": datetime.utcnow().isoformat()
                        }
                        
            return {"success": False, "error": f"Could not get quote for {symbol}"}
            
        except Exception as e:
            self.logger.error(f"Error getting quote for {symbol}: {e}")
            return {"success": False, "error": str(e)}
            
    async def get_market_data_with_indicators(
        self, 
        symbol: str, 
        timeframe: str, 
        periods: int = 100
    ) -> Dict[str, Any]:
        """Get historical data with technical indicators"""
        try:
            from src.services.feature_engine import FeatureEngine
            from src.services.storage_service import StorageService
            from src.models.trading import TimeFrame
            
            # Convert timeframe
            tf_map = {
                "1m": TimeFrame.MINUTE_1,
                "5m": TimeFrame.MINUTE_5,
                "15m": TimeFrame.MINUTE_15,
                "30m": TimeFrame.MINUTE_30,
                "1h": TimeFrame.HOUR_1,
                "1d": TimeFrame.DAY_1
            }
            
            tf = tf_map.get(timeframe, TimeFrame.MINUTE_15)
            
            storage = StorageService()
            await storage.initialize()
            feature_engine = FeatureEngine(storage)
            
            # Get latest indicators
            indicators = await feature_engine.get_latest_indicators(symbol.upper(), tf)
            
            if indicators:
                # Get recent history for trend analysis
                history = await feature_engine.get_indicators_history(symbol.upper(), tf, limit=10)
                
                return {
                    "success": True,
                    "symbol": symbol.upper(),
                    "timeframe": timeframe,
                    "current_indicators": {
                        "price": float(indicators.indicators.close) if hasattr(indicators, 'indicators') else None,
                        "ema_8": float(indicators.ema_8) if indicators.ema_8 else None,
                        "ema_21": float(indicators.ema_21) if indicators.ema_21 else None,
                        "macd": float(indicators.macd) if indicators.macd else None,
                        "macd_signal": float(indicators.macd_signal) if indicators.macd_signal else None,
                        "atr": float(indicators.atr) if indicators.atr else None,
                        "bb_upper": float(indicators.bb_upper) if indicators.bb_upper else None,
                        "bb_lower": float(indicators.bb_lower) if indicators.bb_lower else None,
                        "kc_upper": float(indicators.kc_upper) if indicators.kc_upper else None,
                        "kc_lower": float(indicators.kc_lower) if indicators.kc_lower else None,
                        "squeeze_on": indicators.squeeze_on,
                        "momentum": float(indicators.momentum) if indicators.momentum else None,
                        "volume_ratio": float(indicators.volume_ratio) if indicators.volume_ratio else None
                    },
                    "trend_analysis": self._analyze_trend(history) if history else None,
                    "timestamp": indicators.timestamp.isoformat()
                }
            else:
                return {"success": False, "error": f"No indicator data available for {symbol}"}
                
        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {e}")
            return {"success": False, "error": str(e)}
            
    def _analyze_trend(self, history: List) -> Dict[str, Any]:
        """Analyze trend from indicator history"""
        if len(history) < 3:
            return {"trend": "unknown", "strength": 0}
            
        # Simple trend analysis based on EMA relationship
        recent = history[-3:]
        
        ema_8_trend = []
        ema_21_trend = []
        
        for indicators in recent:
            if indicators.ema_8 and indicators.ema_21:
                ema_8_trend.append(float(indicators.ema_8))
                ema_21_trend.append(float(indicators.ema_21))
                
        if len(ema_8_trend) >= 2:
            ema_8_direction = "up" if ema_8_trend[-1] > ema_8_trend[0] else "down"
            ema_cross = "bullish" if ema_8_trend[-1] > ema_21_trend[-1] else "bearish"
            
            return {
                "trend": ema_8_direction,
                "cross": ema_cross,
                "strength": abs(ema_8_trend[-1] - ema_8_trend[0]) / ema_8_trend[0] * 100
            }
            
        return {"trend": "unknown", "strength": 0}
        
    async def calculate_ai_stop_loss(
        self, 
        symbol: str, 
        entry_price: float, 
        direction: str, 
        timeframe: str,
        risk_percent: float = 2.0
    ) -> Dict[str, Any]:
        """Calculate AI-enhanced stop loss"""
        try:
            from src.indicators.support_resistance import SupportResistanceDetector
            from src.services.data_ingestion import DataIngestionService
            from src.models.trading import TimeFrame
            
            # Get historical data for S&R analysis
            data_service = DataIngestionService()
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=5)
            
            tf_map = {
                "1m": TimeFrame.MINUTE_1,
                "5m": TimeFrame.MINUTE_5,
                "15m": TimeFrame.MINUTE_15,
                "30m": TimeFrame.MINUTE_30,
                "1h": TimeFrame.HOUR_1,
                "1d": TimeFrame.DAY_1
            }
            
            tf = tf_map.get(timeframe, TimeFrame.MINUTE_15)
            
            historical_data = await data_service.get_historical_data(
                symbol=symbol.upper(),
                timeframe=tf,
                start=start_time,
                end=end_time,
                limit=200
            )
            
            if not historical_data:
                # Fallback to simple ATR-based stop
                atr_stop = entry_price * 0.98 if direction == "long" else entry_price * 1.02
                return {
                    "success": True,
                    "stop_price": atr_stop,
                    "method": "fallback_atr",
                    "risk_amount": abs(entry_price - atr_stop),
                    "risk_percent": abs(entry_price - atr_stop) / entry_price * 100
                }
                
            # Use S&R detector
            sr_detector = SupportResistanceDetector()
            
            if direction == "long":
                # Find recent swing low and support
                swing_low = sr_detector.get_recent_swing_low(historical_data, lookback_bars=20)
                sr_levels = sr_detector.detect_support_resistance_levels(historical_data)
                nearest_support = sr_detector.find_nearest_support(entry_price, sr_levels)
                
                # Choose the better stop level
                candidates = []
                if swing_low:
                    candidates.append(swing_low * 0.99)  # Slightly below swing low
                if nearest_support:
                    candidates.append(nearest_support.price * 0.99)
                    
                # Add ATR-based stop as backup
                if historical_data:
                    # Simple ATR calculation
                    recent_data = historical_data[-14:]  # Last 14 periods
                    atr_values = []
                    for i in range(1, len(recent_data)):
                        high_low = float(recent_data[i].high) - float(recent_data[i].low)
                        high_close = abs(float(recent_data[i].high) - float(recent_data[i-1].close))
                        low_close = abs(float(recent_data[i].low) - float(recent_data[i-1].close))
                        true_range = max(high_low, high_close, low_close)
                        atr_values.append(true_range)
                        
                    if atr_values:
                        atr = np.mean(atr_values)
                        candidates.append(entry_price - (atr * 1.5))
                        
                # Choose the stop that gives reasonable risk
                best_stop = None
                for candidate in candidates:
                    risk_pct = (entry_price - candidate) / entry_price * 100
                    if 0.5 <= risk_pct <= risk_percent * 1.5:  # Reasonable risk range
                        if not best_stop or candidate > best_stop:  # Higher stop is better for long
                            best_stop = candidate
                            
                if not best_stop and candidates:
                    best_stop = max(candidates)  # Use highest stop if none in range
                    
                stop_price = best_stop if best_stop else entry_price * 0.98
                
            else:  # short
                # Find recent swing high and resistance
                swing_high = sr_detector.get_recent_swing_high(historical_data, lookback_bars=20)
                sr_levels = sr_detector.detect_support_resistance_levels(historical_data)
                nearest_resistance = sr_detector.find_nearest_resistance(entry_price, sr_levels)
                
                candidates = []
                if swing_high:
                    candidates.append(swing_high * 1.01)  # Slightly above swing high
                if nearest_resistance:
                    candidates.append(nearest_resistance.price * 1.01)
                    
                # Add ATR-based stop
                if historical_data:
                    recent_data = historical_data[-14:]
                    atr_values = []
                    for i in range(1, len(recent_data)):
                        high_low = float(recent_data[i].high) - float(recent_data[i].low)
                        high_close = abs(float(recent_data[i].high) - float(recent_data[i-1].close))
                        low_close = abs(float(recent_data[i].low) - float(recent_data[i-1].close))
                        true_range = max(high_low, high_close, low_close)
                        atr_values.append(true_range)
                        
                    if atr_values:
                        atr = np.mean(atr_values)
                        candidates.append(entry_price + (atr * 1.5))
                        
                best_stop = None
                for candidate in candidates:
                    risk_pct = (candidate - entry_price) / entry_price * 100
                    if 0.5 <= risk_pct <= risk_percent * 1.5:
                        if not best_stop or candidate < best_stop:  # Lower stop is better for short
                            best_stop = candidate
                            
                if not best_stop and candidates:
                    best_stop = min(candidates)
                    
                stop_price = best_stop if best_stop else entry_price * 1.02
                
            return {
                "success": True,
                "stop_price": round(stop_price, 2),
                "method": "ai_technical_analysis",
                "risk_amount": abs(entry_price - stop_price),
                "risk_percent": abs(entry_price - stop_price) / entry_price * 100,
                "technical_levels": {
                    "swing_low": swing_low if direction == "long" else None,
                    "swing_high": swing_high if direction == "short" else None,
                    "support_level": nearest_support.price if direction == "long" and nearest_support else None,
                    "resistance_level": nearest_resistance.price if direction == "short" and nearest_resistance else None
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating AI stop loss: {e}")
            # Fallback to simple percentage stop
            fallback_stop = entry_price * 0.98 if direction == "long" else entry_price * 1.02
            return {
                "success": True,
                "stop_price": fallback_stop,
                "method": "fallback",
                "risk_amount": abs(entry_price - fallback_stop),
                "risk_percent": abs(entry_price - fallback_stop) / entry_price * 100,
                "error": str(e)
            }

    async def find_trading_opportunities(
        self,
        strategy_type: str,
        min_confidence: float = 0.7,
        sectors: Optional[List[str]] = None,
        max_price: Optional[float] = None,
        min_volume: Optional[float] = None
    ) -> Dict[str, Any]:
        """Find trading opportunities based on criteria"""
        try:
            from src.services.signal_engine import SignalEngine
            from src.services.universe_manager import UniverseManager
            from src.models.trading import TimeFrame

            # Get universe of symbols to scan
            universe_manager = UniverseManager()
            await universe_manager.initialize()

            # Filter universe by criteria
            if sectors or max_price or min_volume:
                symbols = universe_manager.get_symbols_by_criteria(
                    max_price=max_price,
                    min_volume=min_volume,
                    sectors=sectors,
                    limit=50
                )
            else:
                symbols = universe_manager.get_active_symbols()[:50]  # Top 50

            # Get signals from signal engine
            signal_engine = SignalEngine()

            # Map strategy types
            timeframes = [TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30]

            all_signals = await signal_engine.get_live_signals(
                timeframes=timeframes,
                top_n=20,
                min_confidence=min_confidence
            )

            # Filter by strategy type if specified
            if strategy_type != "any":
                filtered_signals = []
                for signal in all_signals:
                    if strategy_type == "ttm_squeeze" and signal.signal_type.value == "ttm_squeeze":
                        filtered_signals.append(signal)
                    elif strategy_type == "momentum_breakout" and signal.signal_type.value == "momentum_breakout":
                        filtered_signals.append(signal)
                    # Add more strategy filters as needed
                all_signals = filtered_signals

            # Format opportunities
            opportunities = []
            for signal in all_signals[:10]:  # Top 10 opportunities
                opportunity = {
                    "symbol": signal.symbol,
                    "strategy": signal.signal_type.value,
                    "direction": signal.side.value,
                    "confidence": signal.confidence,
                    "timeframe": signal.timeframe.value,
                    "entry_price": float(signal.entry_price) if signal.entry_price else None,
                    "target_price": float(signal.target_price) if signal.target_price else None,
                    "stop_price": float(signal.stop_price) if signal.stop_price else None,
                    "reasoning": self._generate_signal_reasoning(signal),
                    "timestamp": signal.timestamp.isoformat()
                }
                opportunities.append(opportunity)

            return {
                "success": True,
                "opportunities": opportunities,
                "total_found": len(opportunities),
                "criteria": {
                    "strategy_type": strategy_type,
                    "min_confidence": min_confidence,
                    "sectors": sectors,
                    "max_price": max_price,
                    "min_volume": min_volume
                }
            }

        except Exception as e:
            self.logger.error(f"Error finding trading opportunities: {e}")
            return {"success": False, "error": str(e)}

    def _generate_signal_reasoning(self, signal) -> str:
        """Generate human-readable reasoning for a signal"""
        if signal.signal_type.value == "ttm_squeeze":
            direction = "bullish" if signal.side.value == "buy" else "bearish"
            return f"TTM Squeeze firing with {direction} momentum on {signal.timeframe.value} timeframe. Confidence: {signal.confidence:.1%}"
        else:
            return f"{signal.signal_type.value} signal detected with {signal.confidence:.1%} confidence"

    async def analyze_news_sentiment(
        self,
        symbol: str,
        time_range: str = "1d"
    ) -> Dict[str, Any]:
        """Analyze news sentiment for a symbol"""
        try:
            from src.services.fmp_service import FMPService

            # Calculate time range
            end_date = datetime.utcnow()
            if time_range == "1h":
                start_date = end_date - timedelta(hours=1)
            elif time_range == "4h":
                start_date = end_date - timedelta(hours=4)
            elif time_range == "1d":
                start_date = end_date - timedelta(days=1)
            elif time_range == "3d":
                start_date = end_date - timedelta(days=3)
            else:
                start_date = end_date - timedelta(days=1)

            async with FMPService() as fmp:
                if symbol.upper() == "MARKET":
                    # General market news
                    news = await fmp.get_stock_news(limit=20, from_date=start_date, to_date=end_date)
                else:
                    # Symbol-specific news
                    news = await fmp.get_stock_news(symbol=symbol.upper(), limit=20, from_date=start_date, to_date=end_date)

            if not news:
                return {
                    "success": True,
                    "symbol": symbol.upper(),
                    "sentiment": "neutral",
                    "score": 0.0,
                    "news_count": 0,
                    "summary": "No recent news found"
                }

            # Simple sentiment analysis based on keywords
            positive_keywords = ['beat', 'exceed', 'growth', 'profit', 'gain', 'up', 'bullish', 'positive', 'strong']
            negative_keywords = ['miss', 'decline', 'loss', 'down', 'bearish', 'negative', 'weak', 'fall']

            sentiment_scores = []
            analyzed_news = []

            for article in news[:10]:  # Analyze top 10 articles
                title = article.get('title', '').lower()
                text = article.get('text', '').lower()
                content = f"{title} {text}"

                positive_count = sum(1 for word in positive_keywords if word in content)
                negative_count = sum(1 for word in negative_keywords if word in content)

                if positive_count > negative_count:
                    score = min(1.0, positive_count / 5.0)  # Cap at 1.0
                elif negative_count > positive_count:
                    score = -min(1.0, negative_count / 5.0)  # Cap at -1.0
                else:
                    score = 0.0

                sentiment_scores.append(score)
                analyzed_news.append({
                    "title": article.get('title', ''),
                    "sentiment_score": score,
                    "published": article.get('publishedDate', ''),
                    "url": article.get('url', '')
                })

            # Calculate overall sentiment
            if sentiment_scores:
                avg_score = np.mean(sentiment_scores)
                if avg_score > 0.2:
                    sentiment = "bullish"
                elif avg_score < -0.2:
                    sentiment = "bearish"
                else:
                    sentiment = "neutral"
            else:
                avg_score = 0.0
                sentiment = "neutral"

            return {
                "success": True,
                "symbol": symbol.upper(),
                "sentiment": sentiment,
                "score": round(avg_score, 2),
                "news_count": len(news),
                "time_range": time_range,
                "summary": f"{sentiment.title()} sentiment based on {len(analyzed_news)} recent articles",
                "recent_news": analyzed_news[:5]  # Top 5 articles
            }

        except Exception as e:
            self.logger.error(f"Error analyzing news sentiment: {e}")
            return {"success": False, "error": str(e)}

    async def get_fundamentals_analysis(
        self,
        symbol: str,
        analysis_type: str
    ) -> Dict[str, Any]:
        """Get fundamental analysis for a symbol"""
        try:
            from src.services.fmp_service import FMPService

            async with FMPService() as fmp:
                if analysis_type == "overview":
                    profile = await fmp.get_company_profile(symbol.upper())
                    quote = await fmp.get_quote(symbol.upper())

                    if profile and quote:
                        return {
                            "success": True,
                            "symbol": symbol.upper(),
                            "analysis_type": analysis_type,
                            "data": {
                                "company_name": profile.get('companyName'),
                                "sector": profile.get('sector'),
                                "industry": profile.get('industry'),
                                "market_cap": profile.get('mktCap'),
                                "current_price": quote.get('price'),
                                "pe_ratio": quote.get('pe'),
                                "description": profile.get('description', '')[:500] + "..." if profile.get('description') else None
                            }
                        }

                elif analysis_type == "financials":
                    income_stmt = await fmp.get_financial_statements(symbol.upper(), "income-statement", limit=1)

                    if income_stmt:
                        latest = income_stmt[0]
                        return {
                            "success": True,
                            "symbol": symbol.upper(),
                            "analysis_type": analysis_type,
                            "data": {
                                "revenue": latest.get('revenue'),
                                "gross_profit": latest.get('grossProfit'),
                                "net_income": latest.get('netIncome'),
                                "eps": latest.get('eps'),
                                "period": latest.get('period'),
                                "date": latest.get('date')
                            }
                        }

                elif analysis_type == "ratios":
                    ratios = await fmp.get_financial_ratios(symbol.upper(), limit=1)

                    if ratios:
                        latest = ratios[0]
                        return {
                            "success": True,
                            "symbol": symbol.upper(),
                            "analysis_type": analysis_type,
                            "data": {
                                "current_ratio": latest.get('currentRatio'),
                                "debt_to_equity": latest.get('debtEquityRatio'),
                                "return_on_equity": latest.get('returnOnEquity'),
                                "profit_margin": latest.get('netProfitMargin'),
                                "period": latest.get('period')
                            }
                        }

                elif analysis_type == "insider_activity":
                    insider_data = await fmp.get_insider_trading(symbol.upper(), limit=10)

                    return {
                        "success": True,
                        "symbol": symbol.upper(),
                        "analysis_type": analysis_type,
                        "data": {
                            "recent_transactions": insider_data[:5] if insider_data else [],
                            "transaction_count": len(insider_data) if insider_data else 0
                        }
                    }

            return {"success": False, "error": f"No data available for {analysis_type}"}

        except Exception as e:
            self.logger.error(f"Error getting fundamentals analysis: {e}")
            return {"success": False, "error": str(e)}

    async def calculate_position_size(
        self,
        entry_price: float,
        stop_price: float,
        risk_amount: float,
        account_size: Optional[float] = None
    ) -> Dict[str, Any]:
        """Calculate optimal position size based on risk management"""
        try:
            # Calculate risk per share
            risk_per_share = abs(entry_price - stop_price)

            if risk_per_share == 0:
                return {"success": False, "error": "Entry and stop price cannot be the same"}

            # Calculate position size
            position_size = risk_amount / risk_per_share

            # Calculate position value
            position_value = position_size * entry_price

            # Calculate risk percentage if account size provided
            risk_percent = None
            if account_size and account_size > 0:
                risk_percent = (risk_amount / account_size) * 100

            return {
                "success": True,
                "position_size": round(position_size, 0),  # Whole shares
                "position_value": round(position_value, 2),
                "risk_per_share": round(risk_per_share, 2),
                "total_risk": round(risk_amount, 2),
                "risk_percent": round(risk_percent, 2) if risk_percent else None,
                "recommendation": self._get_position_size_recommendation(position_size, position_value, risk_percent)
            }

        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return {"success": False, "error": str(e)}

    def _get_position_size_recommendation(self, size: float, value: float, risk_pct: Optional[float]) -> str:
        """Get recommendation for position size"""
        if risk_pct:
            if risk_pct > 3:
                return "High risk - consider reducing position size"
            elif risk_pct < 0.5:
                return "Very conservative - could increase position if desired"
            else:
                return "Appropriate risk level"
        else:
            if value > 10000:
                return "Large position - ensure adequate diversification"
            else:
                return "Position size calculated based on risk amount"

    async def create_hedge_strategy(
        self,
        primary_symbol: str,
        position_size: float,
        direction: str,
        hedge_type: str = "sector_etf"
    ) -> Dict[str, Any]:
        """Create hedging strategy for a position"""
        try:
            from src.services.fmp_service import FMPService

            # Get company info to determine sector
            async with FMPService() as fmp:
                profile = await fmp.get_company_profile(primary_symbol.upper())

            if not profile:
                return {"success": False, "error": f"Could not get profile for {primary_symbol}"}

            sector = profile.get('sector', '').lower()

            hedge_strategies = []

            if hedge_type == "sector_etf":
                # Map sectors to ETFs
                sector_etfs = {
                    'technology': 'XLK',
                    'financials': 'XLF',
                    'healthcare': 'XLV',
                    'energy': 'XLE',
                    'consumer discretionary': 'XLY',
                    'consumer staples': 'XLP',
                    'industrials': 'XLI',
                    'materials': 'XLB',
                    'utilities': 'XLU',
                    'real estate': 'XLRE',
                    'communication services': 'XLC'
                }

                hedge_etf = None
                for sector_key, etf in sector_etfs.items():
                    if sector_key in sector:
                        hedge_etf = etf
                        break

                if hedge_etf:
                    # Calculate hedge size (typically 20-50% of position)
                    hedge_ratio = 0.3  # 30% hedge
                    hedge_size = position_size * hedge_ratio

                    # Opposite direction for hedge
                    hedge_direction = "short" if direction == "long" else "long"

                    hedge_strategies.append({
                        "type": "sector_etf",
                        "symbol": hedge_etf,
                        "direction": hedge_direction,
                        "size": round(hedge_size, 0),
                        "ratio": hedge_ratio,
                        "reasoning": f"Hedge {direction} {primary_symbol} position with {hedge_direction} {hedge_etf} to reduce sector risk"
                    })

            elif hedge_type == "market_hedge":
                # Use SPY for market hedge
                hedge_ratio = 0.2  # 20% market hedge
                hedge_size = position_size * hedge_ratio
                hedge_direction = "short" if direction == "long" else "long"

                hedge_strategies.append({
                    "type": "market_hedge",
                    "symbol": "SPY",
                    "direction": hedge_direction,
                    "size": round(hedge_size, 0),
                    "ratio": hedge_ratio,
                    "reasoning": f"Hedge {direction} {primary_symbol} position with {hedge_direction} SPY to reduce market risk"
                })

            elif hedge_type == "correlated_stock":
                # This would require correlation analysis - simplified for now
                hedge_strategies.append({
                    "type": "correlated_stock",
                    "symbol": "To be determined via correlation analysis",
                    "direction": "opposite",
                    "size": position_size * 0.25,
                    "ratio": 0.25,
                    "reasoning": "Requires correlation analysis to identify suitable hedge stock"
                })

            return {
                "success": True,
                "primary_position": {
                    "symbol": primary_symbol.upper(),
                    "direction": direction,
                    "size": position_size
                },
                "hedge_strategies": hedge_strategies,
                "total_strategies": len(hedge_strategies)
            }

        except Exception as e:
            self.logger.error(f"Error creating hedge strategy: {e}")
            return {"success": False, "error": str(e)}

    async def create_trading_plan(self, trades: List[Dict], **kwargs) -> Dict[str, Any]:
        """Create a complete executable trading plan"""
        try:
            from src.services.execution_service import ExecutionService
            from src.models.trading import Order, OrderSide, OrderType

            # Validate trades
            if not trades:
                return {"success": False, "error": "No trades provided"}

            plan_id = str(uuid.uuid4())
            orders = []
            total_risk = 0
            total_expected_return = 0

            for trade in trades:
                # Create order object
                side_map = {
                    "buy": OrderSide.BUY,
                    "sell": OrderSide.SELL,
                    "short": OrderSide.SELL,  # Simplified
                    "cover": OrderSide.BUY   # Simplified
                }

                order = Order(
                    symbol=trade["symbol"].upper(),
                    qty=Decimal(str(trade["quantity"])),
                    side=side_map.get(trade["action"], OrderSide.BUY),
                    order_type=OrderType.LIMIT,
                    limit_price=Decimal(str(trade["entry_price"])),
                    client_order_id=f"holly_{plan_id}_{len(orders)}"
                )

                # Add bracket order if stop and target provided
                if trade.get("stop_price") and trade.get("target_price"):
                    order.order_class = "bracket"
                    order.stop_loss = {"stop_price": float(trade["stop_price"])}
                    order.take_profit = {"limit_price": float(trade["target_price"])}

                orders.append(order)

                # Calculate risk and return
                if trade.get("stop_price"):
                    risk_per_share = abs(trade["entry_price"] - trade["stop_price"])
                    trade_risk = risk_per_share * trade["quantity"]
                    total_risk += trade_risk

                if trade.get("target_price"):
                    return_per_share = abs(trade["target_price"] - trade["entry_price"])
                    trade_return = return_per_share * trade["quantity"]
                    total_expected_return += trade_return

            # Create trading plan
            trading_plan = {
                "id": plan_id,
                "created_at": datetime.utcnow().isoformat(),
                "trades": trades,
                "orders": [order.dict() for order in orders],
                "summary": {
                    "total_trades": len(trades),
                    "total_risk": round(total_risk, 2),
                    "expected_return": round(total_expected_return, 2),
                    "risk_reward_ratio": round(total_expected_return / total_risk, 2) if total_risk > 0 else 0,
                    "confidence_level": kwargs.get("confidence_level", 0.7)
                },
                "execution_ready": True
            }

            return {
                "success": True,
                "plan": trading_plan,
                "plan_id": plan_id,
                "message": f"Created trading plan with {len(trades)} trades. Total risk: ${total_risk:.2f}, Expected return: ${total_expected_return:.2f}"
            }

        except Exception as e:
            self.logger.error(f"Error creating trading plan: {e}")
            return {"success": False, "error": str(e)}

    async def custom_fmp_query(self, endpoint: str, parameters: Optional[Dict] = None) -> Dict[str, Any]:
        """Make custom query to FMP API"""
        try:
            from src.services.fmp_service import FMPService

            async with FMPService() as fmp:
                # Make the custom API call
                result = await fmp._make_request(endpoint, parameters)

                if result:
                    return {
                        "success": True,
                        "endpoint": endpoint,
                        "parameters": parameters,
                        "data": result
                    }
                else:
                    return {
                        "success": False,
                        "error": f"No data returned from endpoint: {endpoint}"
                    }

        except Exception as e:
            self.logger.error(f"Error making custom FMP query: {e}")
            return {"success": False, "error": str(e)}
