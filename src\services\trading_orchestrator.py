"""
Trading orchestrator - coordinates all services to create comprehensive trading plans
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import (
    TradingGoal, TradingPlan, TradingSignal, Order, OrderSide, OrderType, TimeFrame
)
from src.services.llm_service import LLMService
from src.services.signal_engine import SignalEngine
from src.services.execution_service import ExecutionService
from src.services.data_ingestion import DataIngestionService
from src.services.fmp_service import FMPService
from src.services.storage_service import StorageService
from src.indicators.support_resistance import SupportResistanceDetector


class TradingOrchestrator:
    """Orchestrates all trading services to create comprehensive trading plans"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Initialize services
        self.llm_service = LLMService()
        self.signal_engine = SignalEngine()
        self.execution_service = ExecutionService()
        self.data_service = DataIngestionService()
        self.fmp_service = FMPService()
        self.storage = StorageService()
        self.sr_detector = SupportResistanceDetector()
        
        # Active trading plans
        self.active_plans: Dict[str, TradingPlan] = {}
        
    async def process_user_request(self, user_input: str) -> Dict[str, Any]:
        """Process user trading request and create comprehensive plan"""
        try:
            self.logger.info(f"Processing user request: {user_input}")
            
            # Step 1: Interpret user goal using LLM
            llm_response = await self.llm_service.process_user_request(user_input)
            
            if llm_response.get('error'):
                return llm_response
                
            # Extract trading goal if function was called
            trading_goal = None
            if llm_response.get('function_called') == 'interpret_trading_goal':
                goal_data = llm_response.get('function_result', {})
                trading_goal = TradingGoal(**goal_data)
            else:
                # Default goal for simple requests
                trading_goal = TradingGoal(
                    profit_target=Decimal("50"),
                    timeframe="intraday",
                    max_risk_per_trade=Decimal("10"),
                    max_risk_percent=2.0,
                    hedging_requested=False
                )
                
            # Step 2: Generate trading plan
            trading_plan = await self.create_trading_plan(trading_goal)
            
            if not trading_plan:
                return {
                    "response": "I couldn't find any suitable trading opportunities right now. The market conditions don't meet our risk criteria.",
                    "error": False
                }
                
            # Step 3: Generate narrative explanation
            narrative = await self.generate_plan_narrative(trading_plan)
            trading_plan.narrative = narrative
            
            # Step 4: Store the plan
            self.active_plans[trading_plan.id] = trading_plan
            await self.storage.store_cache(f"trading_plan:{trading_plan.id}", trading_plan.dict(), ttl=3600)
            
            return {
                "response": narrative,
                "trading_plan": trading_plan.dict(),
                "plan_id": trading_plan.id,
                "error": False
            }
            
        except Exception as e:
            self.logger.error(f"Error processing user request: {e}")
            return {
                "response": "I encountered an error while processing your request. Please try again.",
                "error": True
            }
            
    async def create_trading_plan(self, goal: TradingGoal) -> Optional[TradingPlan]:
        """Create a comprehensive trading plan based on the goal"""
        try:
            # Step 1: Get live signals
            timeframes = [TimeFrame.MINUTE_5, TimeFrame.MINUTE_15, TimeFrame.MINUTE_30]
            signals = await self.signal_engine.get_live_signals(
                timeframes=timeframes,
                top_n=20,
                min_confidence=0.6
            )
            
            if not signals:
                return None
                
            # Step 2: Filter signals for profit target
            suitable_signals = await self.filter_signals_for_goal(signals, goal)
            
            if not suitable_signals:
                return None
                
            # Step 3: Select best signal(s)
            selected_signals = suitable_signals[:3]  # Top 3 signals
            
            # Step 4: Create orders for each signal
            orders = []
            hedge_orders = []
            total_estimated_profit = Decimal("0")
            total_estimated_risk = Decimal("0")
            
            for signal in selected_signals:
                # Get current quote
                quote = await self.get_current_quote(signal.symbol)
                if not quote:
                    continue
                    
                entry_price = Decimal(str(quote['ask'] if signal.side == OrderSide.BUY else quote['bid']))
                signal.entry_price = entry_price
                
                # Calculate position size
                position_size = await self.calculate_position_size(signal, goal, entry_price)
                
                if position_size <= 0:
                    continue
                    
                # Calculate AI-enhanced stop loss
                stop_price = await self.calculate_ai_stop_loss(signal, entry_price)
                signal.stop_price = stop_price
                
                # Calculate target price
                target_price = await self.calculate_target_price(signal, entry_price, stop_price)
                signal.target_price = target_price
                
                # Create main order
                main_order = Order(
                    symbol=signal.symbol,
                    qty=position_size,
                    side=signal.side,
                    order_type=OrderType.LIMIT,
                    limit_price=entry_price,
                    client_order_id=f"holly_{signal.id}_main"
                )
                
                # Create bracket order with stop and target
                if stop_price and target_price:
                    main_order.order_class = "bracket"
                    main_order.take_profit = {
                        "limit_price": float(target_price)
                    }
                    main_order.stop_loss = {
                        "stop_price": float(stop_price)
                    }
                    
                orders.append(main_order)
                
                # Calculate P&L estimates
                if target_price and stop_price:
                    profit_per_share = target_price - entry_price if signal.side == OrderSide.BUY else entry_price - target_price
                    risk_per_share = entry_price - stop_price if signal.side == OrderSide.BUY else stop_price - entry_price
                    
                    total_estimated_profit += profit_per_share * position_size
                    total_estimated_risk += risk_per_share * position_size
                    
                # Create hedge orders if requested
                if goal.hedging_requested:
                    hedge_order = await self.create_hedge_order(signal, position_size)
                    if hedge_order:
                        hedge_orders.append(hedge_order)
                        
            if not orders:
                return None
                
            # Create trading plan
            plan = TradingPlan(
                id=str(uuid.uuid4()),
                goal=goal,
                signals=selected_signals,
                orders=orders,
                hedge_orders=hedge_orders if hedge_orders else None,
                estimated_profit=total_estimated_profit,
                estimated_risk=total_estimated_risk,
                win_probability=self.calculate_win_probability(selected_signals),
                narrative=""  # Will be filled later
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"Error creating trading plan: {e}")
            return None
            
    async def filter_signals_for_goal(self, signals: List[TradingSignal], goal: TradingGoal) -> List[TradingSignal]:
        """Filter signals that can achieve the profit goal"""
        suitable_signals = []
        
        for signal in signals:
            # Get current quote
            quote = await self.get_current_quote(signal.symbol)
            if not quote:
                continue
                
            entry_price = Decimal(str(quote['ask'] if signal.side == OrderSide.BUY else quote['bid']))
            
            # Estimate potential profit
            if signal.indicators and signal.indicators.atr:
                # Estimate target as 2x ATR move
                atr = signal.indicators.atr
                potential_move = atr * 2
                
                # Calculate shares needed for profit target
                shares_needed = goal.profit_target / potential_move
                
                # Check if position size is reasonable
                position_value = shares_needed * entry_price
                if position_value <= Decimal("10000"):  # Max $10k position
                    suitable_signals.append(signal)
                    
        return suitable_signals
        
    async def calculate_position_size(self, signal: TradingSignal, goal: TradingGoal, entry_price: Decimal) -> Decimal:
        """Calculate appropriate position size"""
        try:
            # Get account info
            account_info = await self.execution_service.get_account_info()
            if not account_info:
                return Decimal("0")
                
            buying_power = Decimal(str(account_info['buying_power']))
            
            # Calculate based on risk percentage
            max_risk_amount = goal.max_risk_per_trade or (buying_power * Decimal(str(goal.max_risk_percent / 100)))
            
            # Estimate stop distance (use ATR if available)
            if signal.indicators and signal.indicators.atr:
                stop_distance = signal.indicators.atr * Decimal("1.5")
            else:
                stop_distance = entry_price * Decimal("0.02")  # 2% default
                
            # Calculate position size based on risk
            position_size = max_risk_amount / stop_distance
            
            # Ensure we don't exceed buying power
            max_shares_by_bp = buying_power / entry_price
            position_size = min(position_size, max_shares_by_bp)
            
            # Round down to whole shares
            return Decimal(int(position_size))
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return Decimal("0")
            
    async def calculate_ai_stop_loss(self, signal: TradingSignal, entry_price: Decimal) -> Optional[Decimal]:
        """Calculate AI-enhanced stop loss using pivot points and support/resistance"""
        try:
            if not signal.indicators or not signal.indicators.atr:
                return None

            # Get historical data for pivot and S&R analysis
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=5)  # 5 days of data

            historical_data = await self.data_service.get_historical_data(
                symbol=signal.symbol,
                timeframe=signal.timeframe,
                start=start_time,
                end=end_time,
                limit=500
            )

            pivot_low = None
            support_level = None

            if historical_data:
                # Find recent swing low
                pivot_low = self.sr_detector.get_recent_swing_low(historical_data, lookback_bars=20)

                # Detect support/resistance levels
                sr_levels = self.sr_detector.detect_support_resistance_levels(historical_data)

                # Find nearest support level
                if signal.side == OrderSide.BUY:
                    nearest_support = self.sr_detector.find_nearest_support(float(entry_price), sr_levels)
                    if nearest_support:
                        support_level = nearest_support.price

            # Use LLM service to calculate AI stop with technical levels
            stop_result = await self.llm_service._calculate_ai_stop(
                symbol=signal.symbol,
                entry_price=float(entry_price),
                timeframe=signal.timeframe.value,
                volatility=float(signal.indicators.atr),
                pivot_low=pivot_low,
                support_level=support_level,
                risk_pct=2.0
            )

            return Decimal(str(stop_result['stop_price']))

        except Exception as e:
            self.logger.error(f"Error calculating AI stop loss: {e}")
            # Fallback to simple ATR-based stop
            if signal.indicators and signal.indicators.atr:
                multiplier = Decimal("1.5")
                if signal.side == OrderSide.BUY:
                    return entry_price - (signal.indicators.atr * multiplier)
                else:
                    return entry_price + (signal.indicators.atr * multiplier)
            return None
            
    async def calculate_target_price(self, signal: TradingSignal, entry_price: Decimal, stop_price: Optional[Decimal]) -> Optional[Decimal]:
        """Calculate target price based on risk/reward ratio"""
        try:
            if not stop_price:
                return None
                
            # Use 2:1 risk/reward ratio
            risk_amount = abs(entry_price - stop_price)
            reward_amount = risk_amount * Decimal("2")
            
            if signal.side == OrderSide.BUY:
                return entry_price + reward_amount
            else:
                return entry_price - reward_amount
                
        except Exception as e:
            self.logger.error(f"Error calculating target price: {e}")
            return None
            
    async def create_hedge_order(self, signal: TradingSignal, position_size: Decimal) -> Optional[Order]:
        """Create hedge order for risk management"""
        # This is a placeholder - would implement actual hedging logic
        # Could use correlated stocks, options, or ETFs
        return None
        
    def calculate_win_probability(self, signals: List[TradingSignal]) -> float:
        """Calculate estimated win probability based on signal confidence"""
        if not signals:
            return 0.0
            
        avg_confidence = sum(signal.confidence for signal in signals) / len(signals)
        return avg_confidence
        
    async def get_current_quote(self, symbol: str) -> Optional[Dict]:
        """Get current quote for a symbol"""
        try:
            # Try data service first
            quote = await self.data_service.get_latest_quote(symbol)
            if quote:
                return quote
                
            # Fallback to FMP
            async with self.fmp_service as fmp:
                quote_data = await fmp.get_quote(symbol)
                if quote_data:
                    return {
                        'symbol': symbol,
                        'bid': quote_data.get('price', 0),
                        'ask': quote_data.get('price', 0),
                        'timestamp': datetime.utcnow()
                    }
                    
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting quote for {symbol}: {e}")
            return None
            
    async def generate_plan_narrative(self, plan: TradingPlan) -> str:
        """Generate human-friendly narrative for the trading plan"""
        try:
            # Prepare plan summary for LLM
            plan_summary = {
                'goal': plan.goal.dict(),
                'signals_count': len(plan.signals),
                'orders_count': len(plan.orders),
                'estimated_profit': float(plan.estimated_profit),
                'estimated_risk': float(plan.estimated_risk),
                'win_probability': plan.win_probability
            }
            
            # Add signal details
            signal_details = []
            for i, signal in enumerate(plan.signals):
                order = plan.orders[i] if i < len(plan.orders) else None
                signal_details.append({
                    'symbol': signal.symbol,
                    'side': signal.side.value,
                    'confidence': signal.confidence,
                    'entry_price': float(signal.entry_price) if signal.entry_price else None,
                    'target_price': float(signal.target_price) if signal.target_price else None,
                    'stop_price': float(signal.stop_price) if signal.stop_price else None,
                    'quantity': float(order.qty) if order else None
                })
                
            prompt = f"""
            Create a clear, step-by-step trading plan explanation for a beginner:
            
            Goal: Make ${plan.goal.profit_target} today
            Plan Summary: {plan_summary}
            Signal Details: {signal_details}
            
            Explain:
            1. What we're going to trade and why
            2. Entry strategy and prices
            3. Risk management (stop losses)
            4. Profit targets
            5. Expected outcome and probability
            
            Keep it simple and encouraging. This is paper trading for learning.
            """
            
            response = await self.llm_service.client.chat.completions.create(
                model=self.llm_service.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error generating plan narrative: {e}")
            return f"Trading plan created with {len(plan.signals)} signals targeting ${plan.goal.profit_target} profit."
            
    async def execute_trading_plan(self, plan_id: str) -> Dict[str, Any]:
        """Execute a trading plan"""
        try:
            if plan_id not in self.active_plans:
                return {"error": "Trading plan not found"}
                
            plan = self.active_plans[plan_id]
            executed_orders = []
            
            # Execute each order
            for order in plan.orders:
                order_id = await self.execution_service.submit_order(order)
                if order_id:
                    executed_orders.append(order_id)
                    self.logger.info(f"Executed order {order_id} for {order.symbol}")
                else:
                    self.logger.error(f"Failed to execute order for {order.symbol}")
                    
            # Execute hedge orders if any
            if plan.hedge_orders:
                for hedge_order in plan.hedge_orders:
                    hedge_id = await self.execution_service.submit_order(hedge_order)
                    if hedge_id:
                        executed_orders.append(hedge_id)
                        
            return {
                "success": True,
                "executed_orders": executed_orders,
                "message": f"Executed {len(executed_orders)} orders for trading plan"
            }
            
        except Exception as e:
            self.logger.error(f"Error executing trading plan: {e}")
            return {"error": "Failed to execute trading plan"}
