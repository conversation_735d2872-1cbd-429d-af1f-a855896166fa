#!/usr/bin/env python3
"""
Test script to verify all API keys are working correctly
"""

import os
import asyncio
import aiohttp
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_alpaca_api():
    """Test Alpaca API connection"""
    print("🔑 Testing Alpaca API...")
    
    try:
        import alpaca_trade_api as tradeapi
        
        api = tradeapi.REST(
            key_id=os.getenv('APCA_API_KEY_ID'),
            secret_key=os.getenv('APCA_API_SECRET_KEY'),
            base_url=os.getenv('APCA_API_BASE_URL'),
            api_version='v2'
        )
        
        account = api.get_account()
        print(f"✅ Alpaca API: Connected successfully!")
        print(f"   Account Status: {account.status}")
        print(f"   Buying Power: ${float(account.buying_power):,.2f}")
        print(f"   Portfolio Value: ${float(account.portfolio_value):,.2f}")
        return True
        
    except Exception as e:
        print(f"❌ Alpaca API: Failed - {e}")
        return False

async def test_fmp_api():
    """Test Financial Modeling Prep API"""
    print("\n🔑 Testing FMP API...")
    
    try:
        api_key = os.getenv('FMP_API_KEY')
        base_url = os.getenv('FMP_BASE_URL')
        
        async with aiohttp.ClientSession() as session:
            # Test with a simple quote request
            url = f"{base_url}/v3/quote/AAPL"
            params = {'apikey': api_key}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data and len(data) > 0:
                        quote = data[0]
                        print(f"✅ FMP API: Connected successfully!")
                        print(f"   AAPL Quote: ${quote.get('price', 'N/A')}")
                        print(f"   Change: {quote.get('change', 'N/A')} ({quote.get('changesPercentage', 'N/A')}%)")
                        return True
                    else:
                        print(f"❌ FMP API: No data returned")
                        return False
                else:
                    print(f"❌ FMP API: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ FMP API: Failed - {e}")
        return False

async def test_openai_api():
    """Test OpenAI API"""
    print("\n🔑 Testing OpenAI API...")
    
    try:
        from openai import AsyncOpenAI
        
        client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Simple test completion
        response = await client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "user", "content": "Say 'Holly AI is ready!' if you can hear me."}
            ],
            max_tokens=10
        )
        
        message = response.choices[0].message.content
        print(f"✅ OpenAI API: Connected successfully!")
        print(f"   Response: {message}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API: Failed - {e}")
        return False

async def main():
    """Run all API tests"""
    print("🧪 Holly AI Trading System - API Key Test\n")
    
    results = []
    
    # Test all APIs
    results.append(await test_alpaca_api())
    results.append(await test_fmp_api())
    results.append(await test_openai_api())
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    if all(results):
        print("🎉 ALL API KEYS WORKING CORRECTLY!")
        print("✅ Alpaca Paper Trading: Ready")
        print("✅ Financial Modeling Prep: Ready") 
        print("✅ OpenAI ChatGPT: Ready")
        print("\n🚀 Holly AI is ready to trade!")
        print("   Run: ./start.sh")
        print("   Then visit: http://localhost:3001")
        print("   Try: 'Make me $50 today'")
    else:
        print("❌ Some API keys failed. Please check:")
        if not results[0]:
            print("   - Alpaca API credentials")
        if not results[1]:
            print("   - FMP API key")
        if not results[2]:
            print("   - OpenAI API key")

if __name__ == "__main__":
    asyncio.run(main())
