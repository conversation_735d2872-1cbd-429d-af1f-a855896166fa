import React, { useState, useRef, useEffect } from 'react';
import {
  Paper,
  TextField,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardActions
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';

const ChatInterface = ({ onPlanExecuted }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: "Hi! I'm <PERSON>, your trading assistant. You can ask me to create trading plans like 'Make me $50 today' and I'll analyze the market and create a comprehensive strategy for you.",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPlan, setCurrentPlan] = useState(null);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/v1/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage
        })
      });

      const data = await response.json();

      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: data.response,
        timestamp: new Date(),
        tradingPlan: data.trading_plan,
        planId: data.plan_id
      };

      setMessages(prev => [...prev, assistantMessage]);

      if (data.trading_plan) {
        setCurrentPlan({
          id: data.plan_id,
          plan: data.trading_plan
        });
      }

    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
        error: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExecutePlan = async (planId) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/v1/execute-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: planId
        })
      });

      const data = await response.json();

      if (data.success) {
        const successMessage = {
          id: Date.now(),
          type: 'assistant',
          content: `✅ Trading plan executed successfully! ${data.executed_orders.length} orders placed.`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
        setCurrentPlan(null);
        
        if (onPlanExecuted) {
          onPlanExecuted();
        }
      } else {
        throw new Error(data.error || 'Failed to execute plan');
      }

    } catch (error) {
      const errorMessage = {
        id: Date.now(),
        type: 'assistant',
        content: `❌ Failed to execute plan: ${error.message}`,
        timestamp: new Date(),
        error: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Paper elevation={3} sx={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderBottom: '1px solid #ddd' }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SmartToyIcon color="primary" />
          Holly AI Chat
        </Typography>
      </Box>

      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        <List>
          {messages.map((message) => (
            <ListItem key={message.id} sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                width: '100%'
              }}>
                <Box sx={{ 
                  maxWidth: '80%',
                  backgroundColor: message.type === 'user' ? '#1976d2' : '#f5f5f5',
                  color: message.type === 'user' ? 'white' : 'black',
                  borderRadius: 2,
                  p: 1.5,
                  mb: 1
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    {message.type === 'user' ? <PersonIcon fontSize="small" /> : <SmartToyIcon fontSize="small" />}
                    <Typography variant="caption">
                      {formatTime(message.timestamp)}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Typography>
                  
                  {message.error && (
                    <Alert severity="error" sx={{ mt: 1 }}>
                      There was an error processing your request.
                    </Alert>
                  )}
                </Box>
              </Box>

              {message.tradingPlan && (
                <Card sx={{ mt: 1, backgroundColor: '#e3f2fd' }}>
                  <CardContent>
                    <Typography variant="subtitle2" gutterBottom>
                      📊 Trading Plan Generated
                    </Typography>
                    <Typography variant="body2">
                      Target Profit: ${message.tradingPlan.goal.profit_target}
                    </Typography>
                    <Typography variant="body2">
                      Estimated Risk: ${message.tradingPlan.estimated_risk}
                    </Typography>
                    <Typography variant="body2">
                      Signals: {message.tradingPlan.signals.length}
                    </Typography>
                    <Typography variant="body2">
                      Win Probability: {(message.tradingPlan.win_probability * 100).toFixed(1)}%
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button 
                      size="small" 
                      variant="contained" 
                      color="primary"
                      onClick={() => handleExecutePlan(message.planId)}
                      disabled={isLoading}
                    >
                      Execute Plan (Paper Trading)
                    </Button>
                  </CardActions>
                </Card>
              )}
            </ListItem>
          ))}
          {isLoading && (
            <ListItem>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={20} />
                <Typography variant="body2" color="text.secondary">
                  Holly is thinking...
                </Typography>
              </Box>
            </ListItem>
          )}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      <Divider />

      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder="Ask Holly to create a trading plan... (e.g., 'Make me $50 today')"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            variant="outlined"
            size="small"
          />
          <Button
            variant="contained"
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            <SendIcon />
          </Button>
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
          Try: "Make me $50 today", "Find me a good trade", "What's the market looking like?"
        </Typography>
      </Box>
    </Paper>
  );
};

export default ChatInterface;
