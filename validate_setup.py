#!/usr/bin/env python3
"""
Validate Holly AI setup and API keys
Run this before starting the system
"""

import os
import sys

def check_env_file():
    """Check if .env file exists and has required keys"""
    print("🔧 Checking .env file...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("   Please copy .env.example to .env and add your API keys")
        return False
    
    # Read .env file
    env_vars = {}
    with open('.env', 'r') as f:
        for line in f:
            if '=' in line and not line.startswith('#'):
                key, value = line.strip().split('=', 1)
                env_vars[key] = value
    
    required_keys = [
        'APCA_API_KEY_ID',
        'APCA_API_SECRET_KEY', 
        'FMP_API_KEY',
        'OPENAI_API_KEY'
    ]
    
    missing_keys = []
    for key in required_keys:
        if key not in env_vars or not env_vars[key] or env_vars[key].startswith('your_'):
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ Missing or invalid API keys: {', '.join(missing_keys)}")
        return False
    
    print("✅ .env file looks good!")
    print(f"   Alpaca Key: {env_vars['APCA_API_KEY_ID'][:8]}...")
    print(f"   FMP Key: {env_vars['FMP_API_KEY'][:8]}...")
    print(f"   OpenAI Key: {env_vars['OPENAI_API_KEY'][:8]}...")
    
    return True

def check_dependencies():
    """Check if required Python packages are installed"""
    print("\n📦 Checking Python dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'openai',
        'alpaca_trade_api',
        'aiohttp',
        'redis',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages installed!")
    return True

def check_docker():
    """Check if Docker is available"""
    print("\n🐳 Checking Docker...")
    
    try:
        import subprocess
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker is available!")
            return True
        else:
            print("❌ Docker not found")
            return False
    except:
        print("❌ Docker not found")
        return False

def main():
    """Run all validation checks"""
    print("🧪 Holly AI Trading System - Setup Validation\n")
    
    checks = [
        check_env_file(),
        check_dependencies(),
        check_docker()
    ]
    
    print("\n" + "="*50)
    print("📊 VALIDATION RESULTS")
    print("="*50)
    
    if all(checks):
        print("🎉 SETUP VALIDATION PASSED!")
        print("\n✅ Environment configured correctly")
        print("✅ Dependencies installed")
        print("✅ Docker available")
        print("\n🚀 Ready to start Holly AI!")
        print("   Run: ./start.sh")
        print("   Then visit: http://localhost:3001")
        print("   Try: 'Make me $50 today'")
        
    else:
        print("❌ SETUP VALIDATION FAILED!")
        print("\nPlease fix the issues above before starting Holly AI.")
        
        if not checks[0]:
            print("\n🔧 To fix .env issues:")
            print("   1. Copy .env.example to .env")
            print("   2. Add your API keys to .env")
            
        if not checks[1]:
            print("\n📦 To fix dependency issues:")
            print("   Run: pip install -r requirements.txt")
            
        if not checks[2]:
            print("\n🐳 To fix Docker issues:")
            print("   Install Docker Desktop from docker.com")

if __name__ == "__main__":
    main()
