#!/usr/bin/env python3
"""
Test Holly AI's comprehensive API access capabilities
"""

import asyncio
import json
from src.services.universal_api_gateway import UniversalAPIGateway

async def test_universal_gateway():
    """Test the Universal API Gateway"""
    print("🧪 Testing Universal API Gateway\n")
    
    gateway = UniversalAPIGateway()
    
    # Test 1: Get available endpoints
    print("📊 Available Endpoints:")
    endpoints_info = gateway.get_available_endpoints()
    print(f"   Alpaca: {endpoints_info['alpaca']['count']} endpoints")
    print(f"   FMP: {endpoints_info['fmp']['count']} endpoints")
    print(f"   Total: {endpoints_info['total_endpoints']} endpoints")
    
    # Test 2: Search endpoints
    print("\n🔍 Searching for 'earnings' endpoints:")
    earnings_endpoints = gateway.search_endpoints("earnings")
    for ep in earnings_endpoints[:5]:
        print(f"   {ep['api']}: {ep['method']} {ep['path']} - {ep['description']}")
    
    # Test 3: Test Alpaca endpoints
    print("\n🏦 Testing Alpaca API:")
    try:
        account_result = await gateway.get_account_info()
        if account_result.get("success"):
            print("   ✅ Account info retrieved successfully")
        else:
            print(f"   ❌ Account info failed: {account_result.get('error')}")
    except Exception as e:
        print(f"   ❌ Account info error: {e}")
    
    # Test 4: Test FMP endpoints
    print("\n📈 Testing FMP API:")
    try:
        quote_result = await gateway.get_quote("AAPL")
        if quote_result.get("success"):
            data = quote_result["data"]
            if data and len(data) > 0:
                price = data[0].get("price", "N/A")
                print(f"   ✅ AAPL quote: ${price}")
            else:
                print("   ⚠️  Quote retrieved but no data")
        else:
            print(f"   ❌ Quote failed: {quote_result.get('error')}")
    except Exception as e:
        print(f"   ❌ Quote error: {e}")
    
    # Test 5: Universal API call
    print("\n🌐 Testing Universal API Call:")
    try:
        result = await gateway.universal_api_call(
            api="fmp",
            endpoint_name="company profile",
            path_params={"symbol": "MSFT"}
        )
        if result.get("success"):
            print("   ✅ Universal API call successful")
        else:
            print(f"   ❌ Universal API call failed: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ Universal API call error: {e}")
    
    print("\n" + "="*50)
    print("🎉 Universal API Gateway Test Complete!")

async def test_holly_functions():
    """Test Holly's enhanced functions"""
    print("\n🤖 Testing Holly AI Functions\n")
    
    from src.core.holly_functions import HollyFunctions
    
    functions = HollyFunctions()
    
    # Test 1: Universal API access
    print("🌐 Testing Universal API Access:")
    try:
        result = await functions.universal_api_access(
            api="fmp",
            endpoint_description="real-time quote",
            symbol="AAPL"
        )
        if result.get("success"):
            print("   ✅ Universal API access working")
        else:
            print(f"   ❌ Universal API access failed: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ Universal API access error: {e}")
    
    # Test 2: Endpoint search
    print("\n🔍 Testing Endpoint Search:")
    try:
        result = await functions.search_api_endpoints("insider trading")
        if result.get("success"):
            count = result.get("count", 0)
            print(f"   ✅ Found {count} insider trading endpoints")
        else:
            print(f"   ❌ Endpoint search failed: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ Endpoint search error: {e}")
    
    # Test 3: Comprehensive analysis
    print("\n📊 Testing Comprehensive Stock Analysis:")
    try:
        result = await functions.get_comprehensive_stock_analysis("AAPL")
        if result.get("success"):
            analysis = result.get("analysis", {})
            available_data = [key for key, value in analysis.items() if value is not None]
            print(f"   ✅ Analysis complete with {len(available_data)} data sources")
            print(f"   📈 Data sources: {', '.join(available_data)}")
        else:
            print(f"   ❌ Comprehensive analysis failed: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ Comprehensive analysis error: {e}")
    
    print("\n" + "="*50)
    print("🎉 Holly AI Functions Test Complete!")

async def demonstrate_capabilities():
    """Demonstrate Holly's new capabilities"""
    print("\n🚀 Holly AI Capabilities Demonstration\n")
    
    gateway = UniversalAPIGateway()
    
    # Show some interesting endpoints
    interesting_queries = [
        "social sentiment",
        "insider trading", 
        "earnings calendar",
        "technical indicators",
        "analyst estimates",
        "institutional holdings"
    ]
    
    print("🔍 Available Specialized Endpoints:")
    for query in interesting_queries:
        results = gateway.search_endpoints(query)
        if results:
            print(f"\n   📊 {query.title()}:")
            for result in results[:3]:  # Show top 3
                print(f"      • {result['api']}: {result['path']}")
    
    print("\n💡 What this means for Holly AI:")
    print("   • Can access ANY financial data through natural language")
    print("   • Combines multiple data sources for comprehensive analysis")
    print("   • Provides institutional-grade market intelligence")
    print("   • Supports complex trading strategies with rich data")
    print("   • Enables real-time monitoring and alerting")
    
    print("\n🎯 Example Holly Conversations:")
    examples = [
        "Get me insider trading data for TSLA",
        "Show me social sentiment for meme stocks",
        "Find undervalued tech stocks with insider buying",
        "What's the earnings calendar for next week?",
        "Analyze NVDA's institutional ownership changes",
        "Get technical indicators for SPY on multiple timeframes"
    ]
    
    for example in examples:
        print(f"   💬 '{example}'")
        print(f"      → Holly automatically finds and calls the right APIs")
    
    print("\n" + "="*50)
    print("🎉 Holly AI is now a comprehensive financial intelligence system!")

async def main():
    """Run all tests"""
    print("🧪 Holly AI - Comprehensive API Access Test Suite")
    print("="*60)
    
    await test_universal_gateway()
    await test_holly_functions()
    await demonstrate_capabilities()
    
    print("\n🎉 ALL TESTS COMPLETE!")
    print("\nHolly AI now has access to 100+ API endpoints and can:")
    print("✅ Call any Alpaca or FMP endpoint through natural language")
    print("✅ Search and discover the right endpoints automatically")
    print("✅ Combine multiple data sources for comprehensive analysis")
    print("✅ Provide institutional-grade market intelligence")
    print("✅ Support complex trading strategies with rich data context")

if __name__ == "__main__":
    asyncio.run(main())
