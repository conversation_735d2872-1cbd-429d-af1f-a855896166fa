"""
Technical indicators library for Holly AI Trading System
"""

import numpy as np
import pandas as pd
import talib
from typing import Dict, List, Optional, Tuple
from decimal import Decimal

from src.core.logging import get_logger
from src.models.trading import MarketData, TechnicalIndicators, TimeFrame


class TechnicalIndicatorEngine:
    """Engine for calculating technical indicators"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
    def calculate_indicators(
        self, 
        data: List[MarketData], 
        timeframe: TimeFrame
    ) -> Optional[TechnicalIndicators]:
        """Calculate all technical indicators for the given data"""
        try:
            if len(data) < 50:  # Need sufficient data for calculations
                return None
                
            # Convert to pandas DataFrame
            df = self._to_dataframe(data)
            
            # Calculate indicators
            indicators = TechnicalIndicators(
                symbol=data[-1].symbol,
                timestamp=data[-1].timestamp,
                timeframe=timeframe
            )
            
            # TTM Squeeze indicators
            bb_upper, bb_lower = self.bollinger_bands(df['close'])
            kc_upper, kc_lower = self.keltner_channels(df['high'], df['low'], df['close'])
            
            indicators.bb_upper = Decimal(str(bb_upper.iloc[-1])) if not pd.isna(bb_upper.iloc[-1]) else None
            indicators.bb_lower = Decimal(str(bb_lower.iloc[-1])) if not pd.isna(bb_lower.iloc[-1]) else None
            indicators.kc_upper = Decimal(str(kc_upper.iloc[-1])) if not pd.isna(kc_upper.iloc[-1]) else None
            indicators.kc_lower = Decimal(str(kc_lower.iloc[-1])) if not pd.isna(kc_lower.iloc[-1]) else None
            
            # Squeeze detection
            if all([indicators.bb_upper, indicators.bb_lower, indicators.kc_upper, indicators.kc_lower]):
                indicators.squeeze_on = (
                    indicators.bb_upper <= indicators.kc_upper and 
                    indicators.bb_lower >= indicators.kc_lower
                )
            
            # Momentum calculation
            momentum = self.ttm_momentum(df['high'], df['low'], df['close'])
            indicators.momentum = Decimal(str(momentum.iloc[-1])) if not pd.isna(momentum.iloc[-1]) else None
            
            # Trend indicators
            ema_8 = self.ema(df['close'], 8)
            ema_21 = self.ema(df['close'], 21)
            indicators.ema_8 = Decimal(str(ema_8.iloc[-1])) if not pd.isna(ema_8.iloc[-1]) else None
            indicators.ema_21 = Decimal(str(ema_21.iloc[-1])) if not pd.isna(ema_21.iloc[-1]) else None
            
            # MACD
            macd, macd_signal, macd_hist = self.macd(df['close'])
            indicators.macd = Decimal(str(macd.iloc[-1])) if not pd.isna(macd.iloc[-1]) else None
            indicators.macd_signal = Decimal(str(macd_signal.iloc[-1])) if not pd.isna(macd_signal.iloc[-1]) else None
            indicators.macd_histogram = Decimal(str(macd_hist.iloc[-1])) if not pd.isna(macd_hist.iloc[-1]) else None
            
            # ATR
            atr = self.atr(df['high'], df['low'], df['close'])
            indicators.atr = Decimal(str(atr.iloc[-1])) if not pd.isna(atr.iloc[-1]) else None
            
            # Volume indicators
            volume_ma = self.sma(df['volume'], 20)
            indicators.volume_ma = Decimal(str(volume_ma.iloc[-1])) if not pd.isna(volume_ma.iloc[-1]) else None
            
            if indicators.volume_ma and indicators.volume_ma > 0:
                indicators.volume_ratio = Decimal(str(df['volume'].iloc[-1])) / indicators.volume_ma
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return None
            
    def _to_dataframe(self, data: List[MarketData]) -> pd.DataFrame:
        """Convert MarketData list to pandas DataFrame"""
        df_data = []
        for item in data:
            df_data.append({
                'timestamp': item.timestamp,
                'open': float(item.open),
                'high': float(item.high),
                'low': float(item.low),
                'close': float(item.close),
                'volume': item.volume
            })
            
        df = pd.DataFrame(df_data)
        df.set_index('timestamp', inplace=True)
        return df
        
    def sma(self, series: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return talib.SMA(series.values, timeperiod=period)
        
    def ema(self, series: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return pd.Series(talib.EMA(series.values, timeperiod=period), index=series.index)
        
    def bollinger_bands(
        self, 
        series: pd.Series, 
        period: int = 20, 
        std_dev: float = 2.0
    ) -> Tuple[pd.Series, pd.Series]:
        """Bollinger Bands"""
        upper, middle, lower = talib.BBANDS(
            series.values, 
            timeperiod=period, 
            nbdevup=std_dev, 
            nbdevdn=std_dev
        )
        return (
            pd.Series(upper, index=series.index),
            pd.Series(lower, index=series.index)
        )
        
    def keltner_channels(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 20, 
        multiplier: float = 1.5
    ) -> Tuple[pd.Series, pd.Series]:
        """Keltner Channels"""
        # Calculate EMA of close
        ema = self.ema(close, period)
        
        # Calculate ATR
        atr = pd.Series(talib.ATR(high.values, low.values, close.values, timeperiod=period), index=close.index)
        
        # Calculate channels
        upper = ema + (multiplier * atr)
        lower = ema - (multiplier * atr)
        
        return upper, lower
        
    def ttm_momentum(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 20
    ) -> pd.Series:
        """TTM Squeeze Momentum"""
        # Calculate the highest high and lowest low over the period
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        # Calculate the midpoint
        midpoint = (highest_high + lowest_low) / 2
        
        # Calculate momentum as close relative to midpoint
        momentum = close - midpoint
        
        # Apply linear regression to smooth the momentum
        momentum_smooth = momentum.rolling(window=period).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == period else np.nan
        )
        
        return momentum_smooth
        
    def macd(
        self, 
        series: pd.Series, 
        fast_period: int = 12, 
        slow_period: int = 26, 
        signal_period: int = 9
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        macd, macd_signal, macd_hist = talib.MACD(
            series.values, 
            fastperiod=fast_period, 
            slowperiod=slow_period, 
            signalperiod=signal_period
        )
        
        return (
            pd.Series(macd, index=series.index),
            pd.Series(macd_signal, index=series.index),
            pd.Series(macd_hist, index=series.index)
        )
        
    def atr(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> pd.Series:
        """Average True Range"""
        atr = talib.ATR(high.values, low.values, close.values, timeperiod=period)
        return pd.Series(atr, index=close.index)
        
    def rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        rsi = talib.RSI(series.values, timeperiod=period)
        return pd.Series(rsi, index=series.index)
        
    def stochastic(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        k_period: int = 14, 
        d_period: int = 3
    ) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        slowk, slowd = talib.STOCH(
            high.values, 
            low.values, 
            close.values, 
            fastk_period=k_period, 
            slowk_period=d_period, 
            slowd_period=d_period
        )
        
        return (
            pd.Series(slowk, index=close.index),
            pd.Series(slowd, index=close.index)
        )
        
    def williams_r(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> pd.Series:
        """Williams %R"""
        willr = talib.WILLR(high.values, low.values, close.values, timeperiod=period)
        return pd.Series(willr, index=close.index)
        
    def obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """On Balance Volume"""
        obv = talib.OBV(close.values, volume.values)
        return pd.Series(obv, index=close.index)
        
    def adx(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> pd.Series:
        """Average Directional Index"""
        adx = talib.ADX(high.values, low.values, close.values, timeperiod=period)
        return pd.Series(adx, index=close.index)
        
    def detect_squeeze_signals(self, indicators_history: List[TechnicalIndicators]) -> Dict[str, bool]:
        """Detect TTM Squeeze signals"""
        if len(indicators_history) < 3:
            return {'squeeze_firing': False, 'momentum_positive': False}
            
        current = indicators_history[-1]
        previous = indicators_history[-2]
        
        # Check if squeeze is firing (was on, now off)
        squeeze_firing = (
            previous.squeeze_on and 
            not current.squeeze_on and
            current.momentum is not None
        )
        
        # Check momentum direction
        momentum_positive = current.momentum and current.momentum > 0
        
        return {
            'squeeze_firing': squeeze_firing,
            'momentum_positive': momentum_positive
        }
