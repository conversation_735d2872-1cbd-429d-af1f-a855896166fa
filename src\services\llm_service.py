"""
LLM service for ChatGPT integration and natural language processing
"""

import json
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Any, Callable
import openai
from openai import AsyncOpenAI

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import TradingGoal, OrderSide


class LLMService:
    """Service for LLM integration and function calling"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = settings.OPENAI_MODEL
        self.temperature = settings.OPENAI_TEMPERATURE
        
        # Function registry for function calling
        self.functions: Dict[str, Dict] = {}
        self.function_handlers: Dict[str, Callable] = {}
        
        # Register core functions
        self._register_core_functions()
        
    def _register_core_functions(self):
        """Register core trading functions for LLM function calling"""
        
        # Goal interpretation function
        self.register_function(
            name="interpret_trading_goal",
            description="Parse user's trading request into structured goal parameters",
            parameters={
                "type": "object",
                "properties": {
                    "user_input": {
                        "type": "string",
                        "description": "The user's natural language trading request"
                    }
                },
                "required": ["user_input"]
            },
            handler=self._interpret_trading_goal
        )
        
        # Market data functions
        self.register_function(
            name="get_stock_quote",
            description="Get real-time price and volume for a given ticker",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, TSLA)"
                    }
                },
                "required": ["symbol"]
            },
            handler=self._get_stock_quote
        )
        
        # AI stop loss calculation
        self.register_function(
            name="calculate_ai_stop",
            description="Calculate AI-enhanced stop-loss price based on technical analysis",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string"},
                    "entry_price": {"type": "number"},
                    "timeframe": {"type": "string", "enum": ["1Min", "5Min", "15Min", "30Min", "1Hour", "1Day"]},
                    "volatility": {"type": "number", "description": "ATR or volatility measure"},
                    "pivot_low": {"type": "number", "description": "Recent swing low"},
                    "support_level": {"type": "number", "description": "Nearest support zone"},
                    "risk_pct": {"type": "number", "description": "Maximum risk percentage"}
                },
                "required": ["symbol", "entry_price", "timeframe", "volatility"]
            },
            handler=self._calculate_ai_stop
        )
        
        # News sentiment analysis
        self.register_function(
            name="analyze_news_sentiment",
            description="Analyze news sentiment for trading impact",
            parameters={
                "type": "object",
                "properties": {
                    "headline": {"type": "string"},
                    "symbol": {"type": "string"},
                    "content": {"type": "string", "description": "Optional news content"}
                },
                "required": ["headline", "symbol"]
            },
            handler=self._analyze_news_sentiment
        )
        
    def register_function(self, name: str, description: str, parameters: Dict, handler: Callable):
        """Register a function for LLM function calling"""
        self.functions[name] = {
            "name": name,
            "description": description,
            "parameters": parameters
        }
        self.function_handlers[name] = handler
        
    async def process_user_request(self, user_input: str, context: Optional[Dict] = None) -> Dict[str, Any]:
        """Process user request using LLM with function calling"""
        try:
            # System prompt for trading assistant
            system_prompt = """You are Holly AI, an expert trading assistant. You help users create profitable trading plans.

Key capabilities:
- Interpret trading goals from natural language
- Analyze market data and technical indicators
- Calculate AI-enhanced stop losses
- Provide comprehensive trading plans with risk management
- Analyze news sentiment for trading impact

When a user asks to make money (e.g., "Make me $50 today"), you should:
1. Interpret their goal using the interpret_trading_goal function
2. Analyze current market conditions
3. Generate a comprehensive trading plan with entry, exit, and stop loss levels
4. Include risk management and hedging if requested
5. Provide clear, step-by-step instructions

Always prioritize risk management and never recommend trades without proper stop losses."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ]
            
            # Add context if provided
            if context:
                context_msg = f"Additional context: {json.dumps(context, default=str)}"
                messages.append({"role": "system", "content": context_msg})
                
            # Make LLM call with function calling
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                functions=list(self.functions.values()),
                function_call="auto",
                temperature=self.temperature
            )
            
            message = response.choices[0].message
            
            # Handle function calls
            if message.function_call:
                function_name = message.function_call.name
                function_args = json.loads(message.function_call.arguments)
                
                # Execute function
                if function_name in self.function_handlers:
                    function_result = await self.function_handlers[function_name](**function_args)
                    
                    # Continue conversation with function result
                    messages.append({
                        "role": "assistant",
                        "content": None,
                        "function_call": {
                            "name": function_name,
                            "arguments": message.function_call.arguments
                        }
                    })
                    messages.append({
                        "role": "function",
                        "name": function_name,
                        "content": json.dumps(function_result, default=str)
                    })
                    
                    # Get final response
                    final_response = await self.client.chat.completions.create(
                        model=self.model,
                        messages=messages,
                        temperature=self.temperature
                    )
                    
                    return {
                        "response": final_response.choices[0].message.content,
                        "function_called": function_name,
                        "function_result": function_result
                    }
                else:
                    return {
                        "response": f"Function {function_name} not found",
                        "error": True
                    }
            else:
                return {
                    "response": message.content,
                    "function_called": None
                }
                
        except Exception as e:
            self.logger.error(f"Error processing user request: {e}")
            return {
                "response": "I'm sorry, I encountered an error processing your request. Please try again.",
                "error": True
            }
            
    async def _interpret_trading_goal(self, user_input: str) -> Dict[str, Any]:
        """Interpret user's trading goal from natural language"""
        try:
            prompt = f"""
            Parse this trading request into structured parameters:
            "{user_input}"
            
            Extract and return JSON with:
            - profit_target: target profit in USD (number)
            - timeframe: "intraday", "daily", "weekly" (string)
            - max_risk_per_trade: maximum risk per trade in USD (number, default 10% of profit_target)
            - max_risk_percent: maximum risk as percentage (number, default 2.0)
            - hedging_requested: whether user wants hedging (boolean)
            - symbols: specific symbols mentioned (array of strings, or null)
            - strategy_preference: any strategy mentioned (string, or null)
            
            Return only valid JSON.
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # Parse JSON response
            result = json.loads(response.choices[0].message.content)
            
            # Create TradingGoal object
            goal = TradingGoal(
                profit_target=Decimal(str(result.get('profit_target', 50))),
                timeframe=result.get('timeframe', 'intraday'),
                max_risk_per_trade=Decimal(str(result.get('max_risk_per_trade', result.get('profit_target', 50) * 0.1))),
                max_risk_percent=result.get('max_risk_percent', 2.0),
                hedging_requested=result.get('hedging_requested', False),
                symbols=result.get('symbols'),
                strategy_preference=result.get('strategy_preference')
            )
            
            return goal.dict()
            
        except Exception as e:
            self.logger.error(f"Error interpreting trading goal: {e}")
            # Return default goal
            return TradingGoal(
                profit_target=Decimal("50"),
                timeframe="intraday",
                max_risk_per_trade=Decimal("5"),
                max_risk_percent=2.0,
                hedging_requested=False
            ).dict()
            
    async def _get_stock_quote(self, symbol: str) -> Dict[str, Any]:
        """Get stock quote (placeholder - would integrate with data service)"""
        # This would integrate with the actual data service
        return {
            "symbol": symbol,
            "price": 150.00,
            "bid": 149.95,
            "ask": 150.05,
            "volume": 1000000,
            "timestamp": "2024-01-01T10:00:00Z"
        }
        
    async def _calculate_ai_stop(
        self, 
        symbol: str, 
        entry_price: float, 
        timeframe: str, 
        volatility: float,
        pivot_low: Optional[float] = None,
        support_level: Optional[float] = None,
        risk_pct: Optional[float] = None
    ) -> Dict[str, Any]:
        """Calculate AI-enhanced stop loss"""
        try:
            prompt = f"""
            You're an expert trader. Calculate an optimal stop-loss for:
            
            Symbol: {symbol}
            Entry Price: ${entry_price}
            Timeframe: {timeframe}
            ATR/Volatility: {volatility}
            Recent Swing Low: {pivot_low or 'Unknown'}
            Support Level: {support_level or 'Unknown'}
            Risk Tolerance: {risk_pct or 2.0}%
            
            Consider:
            1. Give the trade room to breathe (≥1×ATR)
            2. Respect technical levels (below swing lows/support)
            3. Keep risk within tolerance
            
            Return only the stop price as a number.
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            # Parse stop price
            stop_price = float(response.choices[0].message.content.strip())
            
            # Apply guardrails
            min_stop = entry_price - (1.5 * volatility)  # Minimum 1.5x ATR
            if pivot_low:
                min_stop = max(min_stop, pivot_low - 0.01)  # Below pivot low
                
            stop_price = max(stop_price, min_stop)
            
            return {
                "stop_price": stop_price,
                "risk_amount": entry_price - stop_price,
                "risk_percent": ((entry_price - stop_price) / entry_price) * 100
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating AI stop: {e}")
            # Fallback to simple ATR-based stop
            stop_price = entry_price - (1.5 * volatility)
            return {
                "stop_price": stop_price,
                "risk_amount": entry_price - stop_price,
                "risk_percent": ((entry_price - stop_price) / entry_price) * 100
            }
            
    async def _analyze_news_sentiment(
        self, 
        headline: str, 
        symbol: str, 
        content: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze news sentiment for trading impact"""
        try:
            prompt = f"""
            Analyze this news for trading impact on {symbol}:
            
            Headline: {headline}
            {f'Content: {content[:500]}...' if content else ''}
            
            Return JSON with:
            - sentiment: "bullish", "bearish", or "neutral"
            - score: -1.0 to +1.0 (negative = bearish, positive = bullish)
            - magnitude: "low", "medium", "high" (impact strength)
            - reasoning: brief explanation
            
            Return only valid JSON.
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1
            )
            
            return json.loads(response.choices[0].message.content)
            
        except Exception as e:
            self.logger.error(f"Error analyzing news sentiment: {e}")
            return {
                "sentiment": "neutral",
                "score": 0.0,
                "magnitude": "low",
                "reasoning": "Unable to analyze sentiment"
            }
