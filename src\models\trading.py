"""
Trading data models
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class OrderSide(str, Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"
    SHORT = "short"
    COVER = "cover"


class OrderType(str, Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"


class OrderStatus(str, Enum):
    """Order status enumeration"""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    DONE_FOR_DAY = "done_for_day"
    CANCELED = "canceled"
    EXPIRED = "expired"
    REPLACED = "replaced"
    PENDING_CANCEL = "pending_cancel"
    PENDING_REPLACE = "pending_replace"
    ACCEPTED = "accepted"
    PENDING_NEW = "pending_new"
    ACCEPTED_FOR_BIDDING = "accepted_for_bidding"
    STOPPED = "stopped"
    REJECTED = "rejected"
    SUSPENDED = "suspended"
    CALCULATED = "calculated"


class SignalType(str, Enum):
    """Signal type enumeration"""
    TTM_SQUEEZE = "ttm_squeeze"
    MOMENTUM_BREAKOUT = "momentum_breakout"
    MEAN_REVERSION = "mean_reversion"
    TREND_FOLLOWING = "trend_following"


class TimeFrame(str, Enum):
    """Timeframe enumeration"""
    MINUTE_1 = "1Min"
    MINUTE_5 = "5Min"
    MINUTE_15 = "15Min"
    MINUTE_30 = "30Min"
    HOUR_1 = "1Hour"
    DAY_1 = "1Day"


class MarketData(BaseModel):
    """Market data model"""
    symbol: str
    timestamp: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    timeframe: TimeFrame


class TechnicalIndicators(BaseModel):
    """Technical indicators model"""
    symbol: str
    timestamp: datetime
    timeframe: TimeFrame
    
    # TTM Squeeze indicators
    bb_upper: Optional[Decimal] = None
    bb_lower: Optional[Decimal] = None
    kc_upper: Optional[Decimal] = None
    kc_lower: Optional[Decimal] = None
    squeeze_on: Optional[bool] = None
    momentum: Optional[Decimal] = None
    
    # Trend indicators
    ema_8: Optional[Decimal] = None
    ema_21: Optional[Decimal] = None
    macd: Optional[Decimal] = None
    macd_signal: Optional[Decimal] = None
    macd_histogram: Optional[Decimal] = None
    
    # Volatility indicators
    atr: Optional[Decimal] = None
    
    # Volume indicators
    volume_ma: Optional[Decimal] = None
    volume_ratio: Optional[Decimal] = None


class TradingSignal(BaseModel):
    """Trading signal model"""
    id: str
    symbol: str
    timestamp: datetime
    signal_type: SignalType
    side: OrderSide
    confidence: float = Field(ge=0.0, le=1.0)
    entry_price: Decimal
    target_price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    timeframe: TimeFrame
    indicators: TechnicalIndicators
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Order(BaseModel):
    """Order model"""
    id: Optional[str] = None
    symbol: str
    qty: Decimal
    side: OrderSide
    order_type: OrderType
    time_in_force: str = "day"
    limit_price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    trail_price: Optional[Decimal] = None
    trail_percent: Optional[Decimal] = None
    extended_hours: bool = False
    client_order_id: Optional[str] = None
    order_class: Optional[str] = None
    take_profit: Optional[Dict[str, Any]] = None
    stop_loss: Optional[Dict[str, Any]] = None
    status: Optional[OrderStatus] = None
    filled_qty: Optional[Decimal] = None
    filled_avg_price: Optional[Decimal] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class Position(BaseModel):
    """Position model"""
    symbol: str
    qty: Decimal
    side: str
    market_value: Decimal
    cost_basis: Decimal
    unrealized_pl: Decimal
    unrealized_plpc: Decimal
    current_price: Decimal
    lastday_price: Decimal
    change_today: Decimal


class TradingGoal(BaseModel):
    """Trading goal model from user input"""
    profit_target: Decimal
    timeframe: str = "intraday"
    max_risk_per_trade: Optional[Decimal] = None
    max_risk_percent: Optional[float] = None
    hedging_requested: bool = False
    symbols: Optional[List[str]] = None
    strategy_preference: Optional[str] = None


class TradingPlan(BaseModel):
    """Complete trading plan model"""
    id: str
    goal: TradingGoal
    signals: List[TradingSignal]
    orders: List[Order]
    hedge_orders: Optional[List[Order]] = None
    estimated_profit: Decimal
    estimated_risk: Decimal
    win_probability: Optional[float] = None
    narrative: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
