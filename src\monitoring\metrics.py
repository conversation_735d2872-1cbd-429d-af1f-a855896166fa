"""
Prometheus metrics for Holly AI Trading System
"""

import time
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from fastapi import FastAPI, Request
from typing import Callable

# Define metrics
REQUEST_COUNT = Counter(
    'holly_ai_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'holly_ai_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_SIGNALS = Gauge(
    'holly_ai_active_signals',
    'Number of active trading signals'
)

ACTIVE_POSITIONS = Gauge(
    'holly_ai_active_positions',
    'Number of active trading positions'
)

ORDERS_SUBMITTED = Counter(
    'holly_ai_orders_submitted_total',
    'Total number of orders submitted',
    ['symbol', 'side', 'status']
)

ACCOUNT_BALANCE = Gauge(
    'holly_ai_account_balance',
    'Current account balance'
)

PORTFOLIO_VALUE = Gauge(
    'holly_ai_portfolio_value',
    'Current portfolio value'
)

UNREALIZED_PL = Gauge(
    'holly_ai_unrealized_pl',
    'Current unrealized P&L'
)

LLM_REQUESTS = Counter(
    'holly_ai_llm_requests_total',
    'Total number of LLM requests',
    ['function_name', 'status']
)

LLM_DURATION = Histogram(
    'holly_ai_llm_duration_seconds',
    'LLM request duration in seconds',
    ['function_name']
)

DATA_INGESTION_RATE = Counter(
    'holly_ai_data_points_ingested_total',
    'Total number of data points ingested',
    ['symbol', 'timeframe']
)

SIGNAL_GENERATION_RATE = Counter(
    'holly_ai_signals_generated_total',
    'Total number of signals generated',
    ['symbol', 'signal_type', 'timeframe']
)

ERROR_COUNT = Counter(
    'holly_ai_errors_total',
    'Total number of errors',
    ['service', 'error_type']
)


def setup_metrics(app: FastAPI):
    """Setup Prometheus metrics for FastAPI app"""
    
    @app.middleware("http")
    async def metrics_middleware(request: Request, call_next: Callable):
        """Middleware to collect request metrics"""
        start_time = time.time()
        
        # Process request
        response = await call_next(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Extract endpoint info
        method = request.method
        endpoint = request.url.path
        status = response.status_code
        
        # Update metrics
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
        
        return response
    
    # Start Prometheus metrics server
    from src.core.config import settings
    start_http_server(settings.PROMETHEUS_PORT)


class MetricsCollector:
    """Utility class for collecting application metrics"""
    
    @staticmethod
    def record_signal_generated(symbol: str, signal_type: str, timeframe: str):
        """Record a signal generation event"""
        SIGNAL_GENERATION_RATE.labels(
            symbol=symbol,
            signal_type=signal_type,
            timeframe=timeframe
        ).inc()
    
    @staticmethod
    def record_order_submitted(symbol: str, side: str, status: str):
        """Record an order submission event"""
        ORDERS_SUBMITTED.labels(
            symbol=symbol,
            side=side,
            status=status
        ).inc()
    
    @staticmethod
    def record_data_ingestion(symbol: str, timeframe: str, count: int = 1):
        """Record data ingestion events"""
        DATA_INGESTION_RATE.labels(
            symbol=symbol,
            timeframe=timeframe
        ).inc(count)
    
    @staticmethod
    def record_llm_request(function_name: str, status: str, duration: float):
        """Record LLM request metrics"""
        LLM_REQUESTS.labels(
            function_name=function_name,
            status=status
        ).inc()
        
        LLM_DURATION.labels(
            function_name=function_name
        ).observe(duration)
    
    @staticmethod
    def record_error(service: str, error_type: str):
        """Record an error event"""
        ERROR_COUNT.labels(
            service=service,
            error_type=error_type
        ).inc()
    
    @staticmethod
    def update_active_signals(count: int):
        """Update active signals gauge"""
        ACTIVE_SIGNALS.set(count)
    
    @staticmethod
    def update_active_positions(count: int):
        """Update active positions gauge"""
        ACTIVE_POSITIONS.set(count)
    
    @staticmethod
    def update_account_balance(balance: float):
        """Update account balance gauge"""
        ACCOUNT_BALANCE.set(balance)
    
    @staticmethod
    def update_portfolio_value(value: float):
        """Update portfolio value gauge"""
        PORTFOLIO_VALUE.set(value)
    
    @staticmethod
    def update_unrealized_pl(pl: float):
        """Update unrealized P&L gauge"""
        UNREALIZED_PL.set(pl)
