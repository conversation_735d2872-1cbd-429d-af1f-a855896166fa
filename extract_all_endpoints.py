# extract_all_endpoints.py

import requests
from bs4 import BeautifulSoup
import pandas as pd
from urllib.parse import urljoin

# 1) Alpaca v2 via OpenAPI
ALPACA_SPEC_URL = "https://api.alpaca.markets/docs/v2/openapi.json"
# 2) FMP stable docs home
FMP_DOCS_HOME   = "https://site.financialmodelingprep.com/developer/docs/stable"

def list_alpaca_endpoints():
    spec = requests.get(ALPACA_SPEC_URL).json()
    eps = []
    for path, methods in spec.get("paths", {}).items():
        for method in methods:
            eps.append({
                "api":   "Alpaca",
                "method": method.upper(),
                "path":  path,
                "description": spec["paths"][path][method].get("summary","")
            })
    return eps

def gather_fmp_links():
    r = requests.get(FMP_DOCS_HOME)
    r.raise_for_status()
    soup = BeautifulSoup(r.text, "html.parser")
    links = set()
    for a in soup.select("nav a, .sidebar a"):
        href = a.get("href")
        if href and not href.startswith("#"):
            links.add(urljoin(FMP_DOCS_HOME, href))
    return links

def extract_fmp_endpoints(url):
    r = requests.get(url)
    r.raise_for_status()
    soup = BeautifulSoup(r.text, "html.parser")
    eps = []
    for code in soup.find_all("code"):
        txt = code.get_text().strip()
        parts = txt.split()
        if len(parts)>=2 and parts[0] in ("GET","POST","PUT","DELETE","PATCH"):
            eps.append({
                "api": "FMP",
                "method": parts[0],
                "path": parts[1],
                "description": code.find_parent().get_text(separator=" ").replace(txt,"").strip()[:120]
            })
    return eps

def main():
    all_rows = []
    # Alpaca
    all_rows += list_alpaca_endpoints()
    # FMP
    fmp_links = gather_fmp_links()
    for link in fmp_links:
        try:
            all_rows += extract_fmp_endpoints(link)
        except:
            pass
    # dedupe + save
    df = pd.DataFrame(all_rows).drop_duplicates(subset=["api","method","path"])
    df.to_csv("all_endpoints.csv", index=False)
    print(f"Extracted {len(df)} endpoints to all_endpoints.csv")

if __name__=="__main__":
    main()
