"""
Data ingestion service for market data from Alpaca and FMP
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Set
import aiohttp
import websockets
from alpaca_trade_api import REST
from alpaca_trade_api.stream import Stream

from src.core.config import settings
from src.core.logging import get_logger
from src.models.trading import MarketData, TimeFrame
from src.services.storage_service import StorageService


class DataIngestionService:
    """Service for ingesting real-time and historical market data"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.storage = StorageService()
        self.alpaca_client = REST(
            key_id=settings.APCA_API_KEY_ID,
            secret_key=settings.APCA_API_SECRET_KEY,
            base_url=settings.APCA_API_BASE_URL,
            api_version='v2'
        )
        self.stream = Stream(
            key_id=settings.APCA_API_KEY_ID,
            secret_key=settings.APCA_API_SECRET_KEY,
            base_url=settings.APCA_API_BASE_URL,
            data_feed='iex'  # Use IEX for paper trading
        )
        
        self.subscribed_symbols: Set[str] = set()
        self.running = False
        self.tasks: List[asyncio.Task] = []
        
    async def start(self):
        """Start the data ingestion service"""
        self.logger.info("Starting data ingestion service...")
        self.running = True
        
        # Initialize storage
        await self.storage.initialize()
        
        # Start background tasks
        self.tasks.append(asyncio.create_task(self._stream_market_data()))
        self.tasks.append(asyncio.create_task(self._periodic_data_fetch()))
        
        self.logger.info("Data ingestion service started")
        
    async def stop(self):
        """Stop the data ingestion service"""
        self.logger.info("Stopping data ingestion service...")
        self.running = False
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Close storage
        await self.storage.close()
        
        self.logger.info("Data ingestion service stopped")
        
    async def subscribe_symbol(self, symbol: str):
        """Subscribe to real-time data for a symbol"""
        if symbol not in self.subscribed_symbols:
            self.subscribed_symbols.add(symbol)
            self.logger.info(f"Subscribed to {symbol}")
            
    async def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from real-time data for a symbol"""
        if symbol in self.subscribed_symbols:
            self.subscribed_symbols.remove(symbol)
            self.logger.info(f"Unsubscribed from {symbol}")
            
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: TimeFrame, 
        start: datetime, 
        end: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[MarketData]:
        """Get historical market data"""
        try:
            # Convert timeframe to Alpaca format
            alpaca_timeframe = self._convert_timeframe(timeframe)
            
            # Get data from Alpaca
            bars = self.alpaca_client.get_bars(
                symbol,
                alpaca_timeframe,
                start=start.isoformat(),
                end=end.isoformat() if end else None,
                limit=limit,
                adjustment='raw'
            )
            
            # Convert to our format
            market_data = []
            for bar in bars:
                data = MarketData(
                    symbol=symbol,
                    timestamp=bar.timestamp,
                    open=Decimal(str(bar.open)),
                    high=Decimal(str(bar.high)),
                    low=Decimal(str(bar.low)),
                    close=Decimal(str(bar.close)),
                    volume=bar.volume,
                    timeframe=timeframe
                )
                market_data.append(data)
                
            # Store in database
            await self.storage.store_market_data(market_data)
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []
            
    async def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        try:
            quote = self.alpaca_client.get_latest_quote(symbol)
            return {
                'symbol': symbol,
                'bid': float(quote.bid_price),
                'ask': float(quote.ask_price),
                'bid_size': quote.bid_size,
                'ask_size': quote.ask_size,
                'timestamp': quote.timestamp
            }
        except Exception as e:
            self.logger.error(f"Error fetching quote for {symbol}: {e}")
            return None
            
    async def _stream_market_data(self):
        """Stream real-time market data"""
        while self.running:
            try:
                # Set up stream handlers
                async def quote_handler(data):
                    await self._handle_quote(data)
                    
                async def bar_handler(data):
                    await self._handle_bar(data)
                    
                # Subscribe to data
                self.stream.subscribe_quotes(quote_handler, *self.subscribed_symbols)
                self.stream.subscribe_bars(bar_handler, *self.subscribed_symbols)
                
                # Run stream
                await self.stream.run()
                
            except Exception as e:
                self.logger.error(f"Error in market data stream: {e}")
                await asyncio.sleep(5)  # Wait before retrying
                
    async def _handle_quote(self, data):
        """Handle incoming quote data"""
        try:
            quote_data = {
                'symbol': data.symbol,
                'bid': float(data.bid_price),
                'ask': float(data.ask_price),
                'bid_size': data.bid_size,
                'ask_size': data.ask_size,
                'timestamp': data.timestamp
            }
            
            # Store in cache
            await self.storage.store_quote(quote_data)
            
        except Exception as e:
            self.logger.error(f"Error handling quote data: {e}")
            
    async def _handle_bar(self, data):
        """Handle incoming bar data"""
        try:
            market_data = MarketData(
                symbol=data.symbol,
                timestamp=data.timestamp,
                open=Decimal(str(data.open)),
                high=Decimal(str(data.high)),
                low=Decimal(str(data.low)),
                close=Decimal(str(data.close)),
                volume=data.volume,
                timeframe=TimeFrame.MINUTE_1  # Assuming 1-minute bars
            )
            
            # Store in database
            await self.storage.store_market_data([market_data])
            
        except Exception as e:
            self.logger.error(f"Error handling bar data: {e}")
            
    async def _periodic_data_fetch(self):
        """Periodically fetch data for subscribed symbols"""
        while self.running:
            try:
                # Fetch latest bars for all subscribed symbols
                for symbol in self.subscribed_symbols:
                    end_time = datetime.utcnow()
                    start_time = end_time - timedelta(hours=1)
                    
                    await self.get_historical_data(
                        symbol=symbol,
                        timeframe=TimeFrame.MINUTE_1,
                        start=start_time,
                        end=end_time
                    )
                    
                await asyncio.sleep(60)  # Fetch every minute
                
            except Exception as e:
                self.logger.error(f"Error in periodic data fetch: {e}")
                await asyncio.sleep(60)
                
    def _convert_timeframe(self, timeframe: TimeFrame) -> str:
        """Convert our timeframe to Alpaca format"""
        mapping = {
            TimeFrame.MINUTE_1: "1Min",
            TimeFrame.MINUTE_5: "5Min",
            TimeFrame.MINUTE_15: "15Min",
            TimeFrame.MINUTE_30: "30Min",
            TimeFrame.HOUR_1: "1Hour",
            TimeFrame.DAY_1: "1Day"
        }
        return mapping.get(timeframe, "1Min")
