"""
Basic functionality tests for Holly AI Trading System
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta

from src.models.trading import MarketData, TimeFrame, TradingGoal
from src.indicators.technical_indicators import TechnicalIndicatorEngine
from src.services.llm_service import LLMService


class TestTechnicalIndicators:
    """Test technical indicators calculations"""
    
    def setup_method(self):
        self.indicator_engine = TechnicalIndicatorEngine()
        
    def create_sample_data(self, count=100):
        """Create sample market data for testing"""
        data = []
        base_price = 100.0
        
        for i in range(count):
            # Simple price movement simulation
            price_change = (i % 10 - 5) * 0.5  # Oscillating price
            close_price = base_price + price_change + (i * 0.1)
            
            data.append(MarketData(
                symbol="TEST",
                timestamp=datetime.utcnow() - timedelta(minutes=count-i),
                open=Decimal(str(close_price - 0.5)),
                high=Decimal(str(close_price + 1.0)),
                low=Decimal(str(close_price - 1.0)),
                close=Decimal(str(close_price)),
                volume=1000000,
                timeframe=TimeFrame.MINUTE_5
            ))
            
        return data
        
    def test_indicator_calculation(self):
        """Test that indicators can be calculated"""
        data = self.create_sample_data()
        
        indicators = self.indicator_engine.calculate_indicators(data, TimeFrame.MINUTE_5)
        
        assert indicators is not None
        assert indicators.symbol == "TEST"
        assert indicators.timeframe == TimeFrame.MINUTE_5
        
        # Check that key indicators are calculated
        assert indicators.ema_8 is not None
        assert indicators.ema_21 is not None
        assert indicators.atr is not None
        assert indicators.bb_upper is not None
        assert indicators.bb_lower is not None
        assert indicators.kc_upper is not None
        assert indicators.kc_lower is not None
        
    def test_squeeze_detection(self):
        """Test TTM Squeeze detection logic"""
        data = self.create_sample_data()
        
        indicators = self.indicator_engine.calculate_indicators(data, TimeFrame.MINUTE_5)
        
        # Check squeeze detection
        if all([indicators.bb_upper, indicators.bb_lower, indicators.kc_upper, indicators.kc_lower]):
            squeeze_on = (
                indicators.bb_upper <= indicators.kc_upper and 
                indicators.bb_lower >= indicators.kc_lower
            )
            assert isinstance(squeeze_on, bool)
            
    def test_insufficient_data(self):
        """Test behavior with insufficient data"""
        data = self.create_sample_data(10)  # Only 10 data points
        
        indicators = self.indicator_engine.calculate_indicators(data, TimeFrame.MINUTE_5)
        
        # Should return None for insufficient data
        assert indicators is None


class TestTradingGoal:
    """Test trading goal model"""
    
    def test_trading_goal_creation(self):
        """Test creating a trading goal"""
        goal = TradingGoal(
            profit_target=Decimal("50"),
            timeframe="intraday",
            max_risk_per_trade=Decimal("10"),
            max_risk_percent=2.0,
            hedging_requested=False
        )
        
        assert goal.profit_target == Decimal("50")
        assert goal.timeframe == "intraday"
        assert goal.max_risk_per_trade == Decimal("10")
        assert goal.max_risk_percent == 2.0
        assert goal.hedging_requested is False
        
    def test_trading_goal_defaults(self):
        """Test trading goal with defaults"""
        goal = TradingGoal(
            profit_target=Decimal("100")
        )
        
        assert goal.profit_target == Decimal("100")
        assert goal.timeframe == "intraday"  # Default
        assert goal.hedging_requested is False  # Default


class TestLLMService:
    """Test LLM service functionality"""
    
    def setup_method(self):
        self.llm_service = LLMService()
        
    @pytest.mark.asyncio
    async def test_goal_interpretation(self):
        """Test goal interpretation from natural language"""
        # This test requires OpenAI API key to be set
        try:
            result = await self.llm_service._interpret_trading_goal("Make me $50 today")
            
            assert isinstance(result, dict)
            assert 'profit_target' in result
            assert result['profit_target'] == 50
            
        except Exception as e:
            # Skip test if API key not available
            pytest.skip(f"OpenAI API not available: {e}")
            
    def test_function_registration(self):
        """Test that functions are properly registered"""
        assert "interpret_trading_goal" in self.llm_service.functions
        assert "get_stock_quote" in self.llm_service.functions
        assert "calculate_ai_stop" in self.llm_service.functions
        
        # Check function handlers
        assert "interpret_trading_goal" in self.llm_service.function_handlers
        assert callable(self.llm_service.function_handlers["interpret_trading_goal"])


class TestMarketData:
    """Test market data model"""
    
    def test_market_data_creation(self):
        """Test creating market data"""
        data = MarketData(
            symbol="AAPL",
            timestamp=datetime.utcnow(),
            open=Decimal("150.00"),
            high=Decimal("152.00"),
            low=Decimal("149.00"),
            close=Decimal("151.00"),
            volume=1000000,
            timeframe=TimeFrame.MINUTE_5
        )
        
        assert data.symbol == "AAPL"
        assert data.open == Decimal("150.00")
        assert data.high == Decimal("152.00")
        assert data.low == Decimal("149.00")
        assert data.close == Decimal("151.00")
        assert data.volume == 1000000
        assert data.timeframe == TimeFrame.MINUTE_5
        
    def test_market_data_validation(self):
        """Test market data validation"""
        # Test that high >= low
        data = MarketData(
            symbol="TEST",
            timestamp=datetime.utcnow(),
            open=Decimal("100.00"),
            high=Decimal("102.00"),
            low=Decimal("98.00"),
            close=Decimal("101.00"),
            volume=1000,
            timeframe=TimeFrame.MINUTE_1
        )
        
        assert data.high >= data.low
        assert data.high >= data.open
        assert data.high >= data.close
        assert data.low <= data.open
        assert data.low <= data.close


if __name__ == "__main__":
    pytest.main([__file__])
