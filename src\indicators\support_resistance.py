"""
Support and resistance level detection for enhanced stop loss calculation
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from decimal import Decimal
from dataclasses import dataclass

from src.core.logging import get_logger
from src.models.trading import MarketData


@dataclass
class PivotPoint:
    """A pivot point (swing high or low)"""
    timestamp: pd.Timestamp
    price: float
    type: str  # 'high' or 'low'
    strength: int  # Number of bars on each side
    volume: int


@dataclass
class SupportResistanceLevel:
    """A support or resistance level"""
    price: float
    type: str  # 'support' or 'resistance'
    strength: float  # How many times it was tested
    last_test: pd.Timestamp
    confidence: float  # 0-1 confidence score


class SupportResistanceDetector:
    """Detector for support and resistance levels"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
    def find_pivot_points(
        self, 
        data: List[MarketData], 
        lookback: int = 5
    ) -> List[PivotPoint]:
        """Find pivot points (swing highs and lows)"""
        try:
            if len(data) < lookback * 2 + 1:
                return []
                
            # Convert to DataFrame
            df_data = []
            for item in data:
                df_data.append({
                    'timestamp': item.timestamp,
                    'high': float(item.high),
                    'low': float(item.low),
                    'close': float(item.close),
                    'volume': item.volume
                })
                
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            
            pivots = []
            
            # Find pivot highs
            for i in range(lookback, len(df) - lookback):
                current_high = df.iloc[i]['high']
                
                # Check if current high is higher than surrounding bars
                is_pivot_high = True
                for j in range(i - lookback, i + lookback + 1):
                    if j != i and df.iloc[j]['high'] >= current_high:
                        is_pivot_high = False
                        break
                        
                if is_pivot_high:
                    pivots.append(PivotPoint(
                        timestamp=df.index[i],
                        price=current_high,
                        type='high',
                        strength=lookback,
                        volume=df.iloc[i]['volume']
                    ))
                    
            # Find pivot lows
            for i in range(lookback, len(df) - lookback):
                current_low = df.iloc[i]['low']
                
                # Check if current low is lower than surrounding bars
                is_pivot_low = True
                for j in range(i - lookback, i + lookback + 1):
                    if j != i and df.iloc[j]['low'] <= current_low:
                        is_pivot_low = False
                        break
                        
                if is_pivot_low:
                    pivots.append(PivotPoint(
                        timestamp=df.index[i],
                        price=current_low,
                        type='low',
                        strength=lookback,
                        volume=df.iloc[i]['volume']
                    ))
                    
            return pivots
            
        except Exception as e:
            self.logger.error(f"Error finding pivot points: {e}")
            return []
            
    def detect_support_resistance_levels(
        self, 
        data: List[MarketData],
        pivots: Optional[List[PivotPoint]] = None,
        tolerance: float = 0.01  # 1% tolerance for level clustering
    ) -> List[SupportResistanceLevel]:
        """Detect support and resistance levels from price data and pivots"""
        try:
            if not pivots:
                pivots = self.find_pivot_points(data)
                
            if not pivots:
                return []
                
            # Group pivots by price level (with tolerance)
            price_clusters = self._cluster_prices([p.price for p in pivots], tolerance)
            
            levels = []
            
            for cluster_price, cluster_indices in price_clusters.items():
                cluster_pivots = [pivots[i] for i in cluster_indices]
                
                # Determine if it's support or resistance
                pivot_types = [p.type for p in cluster_pivots]
                support_count = pivot_types.count('low')
                resistance_count = pivot_types.count('high')
                
                if support_count > resistance_count:
                    level_type = 'support'
                    strength = support_count
                elif resistance_count > support_count:
                    level_type = 'resistance'
                    strength = resistance_count
                else:
                    # Mixed - use the most recent
                    latest_pivot = max(cluster_pivots, key=lambda p: p.timestamp)
                    level_type = 'support' if latest_pivot.type == 'low' else 'resistance'
                    strength = len(cluster_pivots)
                    
                # Calculate confidence based on strength and recency
                latest_test = max(p.timestamp for p in cluster_pivots)
                confidence = min(1.0, strength / 3.0)  # Max confidence at 3+ tests
                
                levels.append(SupportResistanceLevel(
                    price=cluster_price,
                    type=level_type,
                    strength=strength,
                    last_test=latest_test,
                    confidence=confidence
                ))
                
            # Sort by confidence
            levels.sort(key=lambda x: x.confidence, reverse=True)
            
            return levels
            
        except Exception as e:
            self.logger.error(f"Error detecting support/resistance levels: {e}")
            return []
            
    def _cluster_prices(self, prices: List[float], tolerance: float) -> Dict[float, List[int]]:
        """Cluster prices within tolerance"""
        clusters = {}
        
        for i, price in enumerate(prices):
            # Find existing cluster within tolerance
            cluster_found = False
            
            for cluster_price in clusters.keys():
                if abs(price - cluster_price) / cluster_price <= tolerance:
                    clusters[cluster_price].append(i)
                    cluster_found = True
                    break
                    
            if not cluster_found:
                clusters[price] = [i]
                
        return clusters
        
    def find_nearest_support(
        self, 
        current_price: float, 
        levels: List[SupportResistanceLevel]
    ) -> Optional[SupportResistanceLevel]:
        """Find the nearest support level below current price"""
        support_levels = [l for l in levels if l.type == 'support' and l.price < current_price]
        
        if not support_levels:
            return None
            
        # Return the closest support level
        return min(support_levels, key=lambda x: abs(current_price - x.price))
        
    def find_nearest_resistance(
        self, 
        current_price: float, 
        levels: List[SupportResistanceLevel]
    ) -> Optional[SupportResistanceLevel]:
        """Find the nearest resistance level above current price"""
        resistance_levels = [l for l in levels if l.type == 'resistance' and l.price > current_price]
        
        if not resistance_levels:
            return None
            
        # Return the closest resistance level
        return min(resistance_levels, key=lambda x: abs(current_price - x.price))
        
    def get_recent_swing_low(
        self, 
        data: List[MarketData], 
        lookback_bars: int = 20
    ) -> Optional[float]:
        """Get the most recent swing low within lookback period"""
        try:
            if len(data) < lookback_bars:
                return None
                
            # Look at recent data
            recent_data = data[-lookback_bars:]
            pivots = self.find_pivot_points(recent_data, lookback=3)
            
            # Find the most recent pivot low
            pivot_lows = [p for p in pivots if p.type == 'low']
            
            if not pivot_lows:
                return None
                
            # Return the most recent swing low
            latest_low = max(pivot_lows, key=lambda p: p.timestamp)
            return latest_low.price
            
        except Exception as e:
            self.logger.error(f"Error getting recent swing low: {e}")
            return None
            
    def get_recent_swing_high(
        self, 
        data: List[MarketData], 
        lookback_bars: int = 20
    ) -> Optional[float]:
        """Get the most recent swing high within lookback period"""
        try:
            if len(data) < lookback_bars:
                return None
                
            # Look at recent data
            recent_data = data[-lookback_bars:]
            pivots = self.find_pivot_points(recent_data, lookback=3)
            
            # Find the most recent pivot high
            pivot_highs = [p for p in pivots if p.type == 'high']
            
            if not pivot_highs:
                return None
                
            # Return the most recent swing high
            latest_high = max(pivot_highs, key=lambda p: p.timestamp)
            return latest_high.price
            
        except Exception as e:
            self.logger.error(f"Error getting recent swing high: {e}")
            return None
