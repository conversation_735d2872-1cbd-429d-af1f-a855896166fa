"""
Universe manager for maintaining and updating the tradeable symbol universe
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional
from dataclasses import dataclass

from src.core.config import settings
from src.core.logging import get_logger
from src.services.fmp_service import FMPService
from src.services.storage_service import StorageService


@dataclass
class SymbolInfo:
    """Information about a tradeable symbol"""
    symbol: str
    name: str
    sector: str
    industry: str
    market_cap: float
    avg_volume: float
    price: float
    exchange: str
    is_etf: bool
    last_updated: datetime


class UniverseManager:
    """Manages the tradeable symbol universe"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.storage = StorageService()
        self.fmp_service = FMPService()
        
        # Symbol universe
        self.universe: Dict[str, SymbolInfo] = {}
        self.active_symbols: Set[str] = set()
        
        # Default universe (high-quality, liquid stocks)
        self.core_universe = {
            # Tech
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX', 'ADBE', 'CRM',
            'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO', 'TXN', 'INTU', 'CSCO', 'IBM', 'UBER',
            
            # Finance
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK', 'SCHW', 'USB',
            
            # Healthcare
            'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR', 'BMY', 'AMGN',
            
            # Consumer
            'WMT', 'HD', 'PG', 'KO', 'PEP', 'MCD', 'NKE', 'SBUX', 'TGT', 'COST',
            
            # Industrial
            'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'LMT', 'RTX', 'DE', 'FDX',
            
            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'MPC', 'VLO', 'PSX', 'OXY', 'HAL',
            
            # ETFs
            'SPY', 'QQQ', 'IWM', 'DIA', 'VTI', 'VOO', 'VEA', 'VWO', 'GLD', 'SLV'
        }
        
    async def initialize(self):
        """Initialize the universe manager"""
        await self.storage.initialize()
        
        # Load universe from cache or build fresh
        cached_universe = await self.storage.get_cache("symbol_universe")
        
        if cached_universe:
            self.logger.info("Loading symbol universe from cache")
            self.universe = {
                symbol: SymbolInfo(**data) 
                for symbol, data in cached_universe.items()
            }
        else:
            self.logger.info("Building fresh symbol universe")
            await self.build_universe()
            
        # Set initial active symbols
        self.active_symbols = set(list(self.universe.keys())[:50])  # Start with top 50
        
    async def build_universe(self):
        """Build the symbol universe from scratch"""
        try:
            self.logger.info("Building symbol universe...")
            
            # Start with core universe
            for symbol in self.core_universe:
                await self.add_symbol_to_universe(symbol)
                
            # Add high-volume stocks from screening
            await self.add_screened_symbols()
            
            # Cache the universe
            universe_data = {
                symbol: info.__dict__ 
                for symbol, info in self.universe.items()
            }
            await self.storage.store_cache("symbol_universe", universe_data, ttl=86400)
            
            self.logger.info(f"Built universe with {len(self.universe)} symbols")
            
        except Exception as e:
            self.logger.error(f"Error building universe: {e}")
            
    async def add_symbol_to_universe(self, symbol: str) -> bool:
        """Add a symbol to the universe with full information"""
        try:
            async with self.fmp_service as fmp:
                # Get company profile
                profile = await fmp.get_company_profile(symbol)
                if not profile:
                    return False
                    
                # Get quote for current price and volume
                quote = await fmp.get_quote(symbol)
                if not quote:
                    return False
                    
                # Create symbol info
                symbol_info = SymbolInfo(
                    symbol=symbol,
                    name=profile.get('companyName', symbol),
                    sector=profile.get('sector', 'Unknown'),
                    industry=profile.get('industry', 'Unknown'),
                    market_cap=profile.get('mktCap', 0),
                    avg_volume=quote.get('avgVolume', 0),
                    price=quote.get('price', 0),
                    exchange=profile.get('exchangeShortName', 'NASDAQ'),
                    is_etf=profile.get('isEtf', False),
                    last_updated=datetime.utcnow()
                )
                
                self.universe[symbol] = symbol_info
                return True
                
        except Exception as e:
            self.logger.error(f"Error adding symbol {symbol}: {e}")
            return False
            
    async def add_screened_symbols(self):
        """Add symbols from screening criteria"""
        try:
            async with self.fmp_service as fmp:
                # Screen for high-volume, mid-to-large cap stocks
                screened = await fmp.get_stock_screener(
                    market_cap_more_than=1000000000,  # $1B+ market cap
                    volume_more_than=1000000,  # 1M+ average volume
                    price_more_than=10,  # $10+ price
                    price_lower_than=1000,  # Under $1000
                    is_actively_trading=True,
                    limit=200
                )
                
                # Add top screened symbols
                for stock in screened[:100]:  # Top 100
                    symbol = stock.get('symbol')
                    if symbol and symbol not in self.universe:
                        await self.add_symbol_to_universe(symbol)
                        
        except Exception as e:
            self.logger.error(f"Error adding screened symbols: {e}")
            
    async def update_universe_daily(self):
        """Daily update of universe data"""
        try:
            self.logger.info("Starting daily universe update...")
            
            # Update existing symbols
            for symbol in list(self.universe.keys()):
                success = await self.update_symbol_info(symbol)
                if not success:
                    # Remove symbols that are no longer valid
                    del self.universe[symbol]
                    if symbol in self.active_symbols:
                        self.active_symbols.remove(symbol)
                        
            # Add new symbols from screening
            await self.add_screened_symbols()
            
            # Update active symbols based on volume and volatility
            await self.update_active_symbols()
            
            # Cache updated universe
            universe_data = {
                symbol: info.__dict__ 
                for symbol, info in self.universe.items()
            }
            await self.storage.store_cache("symbol_universe", universe_data, ttl=86400)
            
            self.logger.info(f"Universe update complete: {len(self.universe)} total, {len(self.active_symbols)} active")
            
        except Exception as e:
            self.logger.error(f"Error updating universe: {e}")
            
    async def update_symbol_info(self, symbol: str) -> bool:
        """Update information for a single symbol"""
        try:
            async with self.fmp_service as fmp:
                quote = await fmp.get_quote(symbol)
                if not quote:
                    return False
                    
                if symbol in self.universe:
                    info = self.universe[symbol]
                    info.price = quote.get('price', info.price)
                    info.avg_volume = quote.get('avgVolume', info.avg_volume)
                    info.last_updated = datetime.utcnow()
                    
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating symbol {symbol}: {e}")
            return False
            
    async def update_active_symbols(self):
        """Update the active trading symbols based on criteria"""
        try:
            # Score symbols based on volume, volatility, and market cap
            scored_symbols = []
            
            for symbol, info in self.universe.items():
                # Skip if no recent data
                if not info.last_updated or (datetime.utcnow() - info.last_updated).days > 1:
                    continue
                    
                # Calculate score
                volume_score = min(info.avg_volume / 1000000, 10)  # Max 10 for 10M+ volume
                mcap_score = min(info.market_cap / 1000000000, 10)  # Max 10 for 10B+ market cap
                price_score = 5 if 20 <= info.price <= 500 else 2  # Prefer $20-500 range
                
                total_score = volume_score + mcap_score + price_score
                
                scored_symbols.append((symbol, total_score))
                
            # Sort by score and take top symbols
            scored_symbols.sort(key=lambda x: x[1], reverse=True)
            
            # Update active symbols (top 100)
            self.active_symbols = set([symbol for symbol, score in scored_symbols[:100]])
            
            # Always include core universe
            self.active_symbols.update(self.core_universe)
            
            self.logger.info(f"Updated active symbols: {len(self.active_symbols)} symbols")
            
        except Exception as e:
            self.logger.error(f"Error updating active symbols: {e}")
            
    def get_active_symbols(self) -> List[str]:
        """Get list of active trading symbols"""
        return list(self.active_symbols)
        
    def get_symbols_by_sector(self, sector: str) -> List[str]:
        """Get symbols filtered by sector"""
        return [
            symbol for symbol, info in self.universe.items()
            if info.sector.lower() == sector.lower() and symbol in self.active_symbols
        ]
        
    def get_symbols_by_criteria(
        self, 
        min_market_cap: Optional[float] = None,
        max_market_cap: Optional[float] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        min_volume: Optional[float] = None,
        sectors: Optional[List[str]] = None,
        limit: int = 50
    ) -> List[str]:
        """Get symbols matching specific criteria"""
        filtered = []
        
        for symbol, info in self.universe.items():
            if symbol not in self.active_symbols:
                continue
                
            # Apply filters
            if min_market_cap and info.market_cap < min_market_cap:
                continue
            if max_market_cap and info.market_cap > max_market_cap:
                continue
            if min_price and info.price < min_price:
                continue
            if max_price and info.price > max_price:
                continue
            if min_volume and info.avg_volume < min_volume:
                continue
            if sectors and info.sector not in sectors:
                continue
                
            filtered.append(symbol)
            
        return filtered[:limit]
        
    def get_symbol_info(self, symbol: str) -> Optional[SymbolInfo]:
        """Get information for a specific symbol"""
        return self.universe.get(symbol)
