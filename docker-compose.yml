version: '3.8'

services:
  holly-ai:
    build: .
    ports:
      - "8080:8080"
      - "8000:8000"  # Prometheus metrics
    environment:
      - ENVIRONMENT=development
    env_file:
      - .env
    depends_on:
      - redis
      - influxdb
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  influxdb:
    image: influxdb:2.7
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=password123
      - DOCKER_INFLUXDB_INIT_ORG=holly-ai
      - DOCKER_INFLUXDB_INIT_BUCKET=market-data
    volumes:
      - influxdb_data:/var/lib/influxdb2
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.2.0
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - influxdb
    restart: unless-stopped

  web-dashboard:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
    depends_on:
      - holly-ai
    restart: unless-stopped

volumes:
  redis_data:
  influxdb_data:
  grafana_data:
