# 🚀 Holly AI Trading System - Startup Guide

## ✅ Your API Keys Are Configured!

Your API keys have been set up in the `.env` file:
- **Alpaca Paper Trading**: PKI0KNC8HXZURYRA4OMC
- **Financial Modeling Prep**: K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7  
- **OpenAI ChatGPT**: ********************************************************************************************************************************************************************

## 🏃‍♂️ Quick Start (Recommended)

### Option 1: Docker Compose (Easiest)
```bash
# Start everything with one command
./start.sh

# Or manually:
docker-compose up -d --build
```

### Option 2: Manual Setup
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Validate setup
python validate_setup.py

# 3. Start the backend
python main.py

# 4. In another terminal, start frontend
cd frontend
npm install
npm start
```

## 🌐 Access Points

Once started, you can access:

- **🤖 Holly AI Chat**: http://localhost:3001
- **📊 API Documentation**: http://localhost:8080/docs
- **📈 Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **🔍 Prometheus Metrics**: http://localhost:8000

## 💬 Try These Commands with Holly

Once you're in the web interface at http://localhost:3001, try chatting with Holly:

### **Profit-Focused Requests**
- "Make me $50 today"
- "I want to make $200 this week with low risk"
- "Find me a safe trade that could make $25"

### **Market Analysis**
- "What's AAPL looking like right now?"
- "Show me some TTM Squeeze setups"
- "Find me momentum plays under $100"
- "What's the sentiment on NVDA?"

### **Risk Management**
- "I need a hedge for my 100 shares of Tesla"
- "Calculate position size for MSFT at $300 with a $5 stop"
- "How much should I risk on this trade?"

### **Learning Questions**
- "Explain why this trade makes sense"
- "What should I watch out for?"
- "How do you calculate stop losses?"

## 🔧 Troubleshooting

### If Holly doesn't respond:
1. Check that all services are running: `docker-compose ps`
2. Check logs: `docker-compose logs holly-backend`
3. Verify API keys in `.env` file

### If you get API errors:
1. Run validation: `python validate_setup.py`
2. Check your internet connection
3. Verify API key limits haven't been exceeded

### If Docker issues:
1. Make sure Docker Desktop is running
2. Try: `docker-compose down && docker-compose up -d --build`

## 🛡️ Safety Features

- **Paper Trading Only**: All trades are simulated - no real money at risk
- **Risk Management**: Every trade includes proper stop losses
- **Position Limits**: Maximum position sizes and counts
- **Circuit Breakers**: Automatic halt on excessive losses

## 📚 What Holly Can Do

Holly AI is a complete LLM-first trading system that can:

1. **Understand Natural Language**: Chat like you're talking to a professional trader
2. **Analyze Markets**: Real-time data, technical indicators, sentiment analysis
3. **Generate Signals**: TTM Squeeze, momentum, mean reversion strategies
4. **Calculate Risk**: AI-enhanced stop losses using support/resistance
5. **Size Positions**: Optimal position sizing based on account risk
6. **Create Plans**: Complete executable trading plans with reasoning
7. **Execute Trades**: Submit orders to paper trading account
8. **Monitor Performance**: Track P&L and provide updates

## 🎯 Example Conversation

**You:** "Make me $50 today"

**Holly:** "I'll analyze the market and create a plan for you...

*[Holly automatically scans 100+ stocks, finds TTM Squeeze setup]*

I found a great opportunity on AAPL! Here's my complete plan:

**Trade Setup:**
- BUY 25 shares AAPL at $150.50
- Stop Loss: $148.75 (AI-calculated using recent support)
- Target: $152.25 (2:1 risk/reward)
- Risk: $43.75 | Potential Profit: $87.50

**Why this trade:**
1. TTM Squeeze just fired with bullish momentum
2. Strong volume confirmation
3. AI analysis shows solid support at $148.75

Would you like me to execute this plan?"

**You:** "Yes, do it"

**Holly:** "✅ Plan executed! Order placed for 25 AAPL shares. I'll monitor and update you on progress."

## 🎉 You're Ready!

Holly AI is now configured and ready to help you learn trading through natural conversation. Remember:

- This is **paper trading** - perfect for learning without risk
- Holly explains everything in plain English
- Ask questions anytime - Holly loves to teach!
- Start simple and work your way up to more complex strategies

**Happy Trading with Holly AI! 🤖📈**
