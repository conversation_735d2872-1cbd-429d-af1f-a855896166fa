#!/usr/bin/env python3
"""
Holly AI Trading System
Main application entry point
"""

import asyncio
import logging
import os
import signal
import sys
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from src.api.routes import api_router
from src.core.config import settings
from src.core.logging import setup_logging
from src.services.data_ingestion import DataIngestionService
from src.services.signal_engine import SignalEngine
from src.services.execution_service import ExecutionService
from src.services.llm_service import LLMService
from src.services.scheduler_service import SchedulerService
from src.services.universe_manager import UniverseManager
from src.monitoring.metrics import setup_metrics

# Load environment variables
load_dotenv()

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Global services
data_service = None
signal_engine = None
execution_service = None
llm_service = None
scheduler_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global data_service, signal_engine, execution_service, llm_service, scheduler_service

    logger.info("Starting Holly AI Trading System...")

    try:
        # Initialize services
        data_service = DataIngestionService()
        signal_engine = SignalEngine()
        execution_service = ExecutionService()
        llm_service = LLMService()
        scheduler_service = SchedulerService()

        # Start background services
        await data_service.start()
        await signal_engine.start()
        await scheduler_service.start()
        
        logger.info("All services started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start services: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        logger.info("Shutting down services...")
        if data_service:
            await data_service.stop()
        if signal_engine:
            await signal_engine.stop()
        if scheduler_service:
            await scheduler_service.stop()
        logger.info("Shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application"""
    
    app = FastAPI(
        title="Holly AI Trading System",
        description="AI-powered trading system with ChatGPT integration",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")
    
    # Setup metrics endpoint
    setup_metrics(app)
    
    return app


def handle_signal(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main entry point"""
    # Register signal handlers
    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)
    
    # Create application
    app = create_app()
    
    # Run the application
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=settings.PORT,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
